define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'wefinancial/report_item/index' + location.search,
                    add_url: 'wefinancial/report_item/add',
                    edit_url: 'wefinancial/report_item/edit',
                    del_url: 'wefinancial/report_item/del',
                    multi_url: 'wefinancial/report_item/multi',
                    import_url: 'wefinancial/report_item/import',
                    table: 'wefinancial_report_item',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'template_id', title: __('Template_id')},
                        {field: 'item_name', title: __('Item_name'), operate: 'LIKE'},
                        {field: 'line_number', title: __('Line_number')},
                        {field: 'column_position', title: __('Column_position'), searchList: {"left":__('Column_position left'),"right":__('Column_position right'),"single":__('Column_position single')}, formatter: Table.api.formatter.normal},
                        {field: 'type', title: __('Type'), searchList: {"header":__('Type header'),"detail":__('Type detail'),"subtotal":__('Type subtotal'),"total":__('Type total'),"section":__('Type section')}, formatter: Table.api.formatter.normal},
                        {field: 'calculation_type', title: __('Calculation_type'), searchList: {"formula":__('Calculation_type formula')," direct":__('Calculation_type  direct')}, formatter: Table.api.formatter.normal},
                        {field: 'weigh', title: __('Weigh'), operate: false},
                        {field: 'bold', title: __('Bold'), searchList: {"1":__('Bold 1'),"0":__('Bold 0')}, formatter: Table.api.formatter.normal},
                        {field: 'indent', title: __('Indent')},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status 1'),"0":__('Status 0')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
