define(['jquery', 'bootstrap', 'backend', 'table', 'form', '/assets/addons/wefinancial/js/common.js'], function ($, undefined, Backend, Table, Form, wef) {

  var Controller = {
    index: function () {
      // 初始化表单验证 - 使用自定义成功回调
      Form.api.bindevent($("#settings-form"), function (data, ret) {
        // 表单提交成功后的处理
        setTimeout(function () {
          Layer.confirm('设置保存成功！是否立即进行初始余额设置？', function (index) {
            // 重新加载当前标签页
            Backend.api.addtabs('wefinancial/initial_balance');
            Layer.close(index);
            location.reload()
          }, function (index) {
            Layer.close(index);
            location.reload()
          })
        }, 500);
      });

      // 启用期间输入格式化
      $('#c-initial_period').on('input', function () {
        var value = $(this).val().replace(/[^\d-]/g, '');
        if (value.length > 7) {
          value = value.substring(0, 7);
        }
        if (value.length >= 4 && value.indexOf('-') === -1) {
          value = value.substring(0, 4) + '-' + value.substring(4);
        }
        $(this).val(value);
      });

      // 纳税人识别号输入限制
      $('#c-tax_no').on('input', function () {
        var value = $(this).val().replace(/[^A-Za-z0-9]/g, '');
        if (value.length > 18) {
          value = value.substring(0, 18);
        }
        $(this).val(value.toUpperCase());
      });

      // 公司名称和纳税人名称联动
      $('#c-company_name').on('blur', function () {
        var companyName = $(this).val();
        var taxName = $('#c-tax_name').val();
        if (companyName && !taxName) {
          $('#c-tax_name').val(companyName);
        }
      });

      // 重置按钮事件 - 危险操作确认
      $('#btn-reset').on('click', function () {
        Controller.api.showResetConfirmation();
      });


      // 输入验证提示
      Controller.api.bindValidationTips();
    },

    api: {
      // 绑定验证提示
      bindValidationTips: function () {
        // 启用期间验证
        $('#c-initial_period').on('blur', function () {
          var value = $(this).val();
          var pattern = /^\d{4}-\d{2}$/;
          if (value && !pattern.test(value)) {
            $(this).closest('.form-group').addClass('has-error');
            $(this).siblings('.help-block').html('<span class="text-danger">格式错误，请使用YYYY-MM格式</span>');
          } else {
            $(this).closest('.form-group').removeClass('has-error');
            $(this).siblings('.help-block').html('格式：YYYY-MM，如：2025-01');
          }
        });

        // 纳税人识别号验证
        $('#c-tax_no').on('blur', function () {
          var value = $(this).val();
          if (value && value.length < 15) {
            $(this).closest('.form-group').addClass('has-error');
            $(this).siblings('.help-block').html('<span class="text-danger">纳税人识别号长度不足</span>');
          } else {
            $(this).closest('.form-group').removeClass('has-error');
            $(this).siblings('.help-block').html('统一社会信用代码或税务登记号');
          }
        });
      },
    }
  };

  return Controller;
});
