define(['jquery', 'bootstrap', 'backend', 'table', 'form', '/assets/addons/wefinancial/js/common.js'], function ($, undefined, Backend, Table, Form, wef) {

  var Controller = {
    index: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          index_url: 'wefinancial/voucher/index' + location.search,
          add_url: 'wefinancial/voucher/add',
          edit_url: 'wefinancial/voucher/edit',
          del_url: 'wefinancial/voucher/del',
          multi_url: 'wefinancial/voucher/multi',
          import_url: 'wefinancial/voucher/import',
          table: 'wefinancial_voucher'
        },
      });

      var table = $("#table");

      // 初始化表格
      table.bootstrapTable({
        url: $.fn.bootstrapTable.defaults.extend.index_url,
        pk: 'id',
        sortName: 'id',
        fixedColumns: true,
        fixedRightNumber: 1,
        striped: false,
        columns: [
          [
            {
              field: 'checkbox',
              checkbox: true
            },
            {
              field: 'id',
              title: __('Id'),
              formatter: wef.TableFormatter.firstEntryOnly
            },
            {
              field: 'voucher_no',
              title: __('Voucher_no'),
              operate: 'LIKE',
              formatter: wef.TableFormatter.firstEntryOnly
            },
            {
              field: 'period',
              title: __('Period'),
              operate: 'LIKE',
              formatter: wef.TableFormatter.firstEntryOnly
            },
            {
              field: 'voucher_date',
              title: __('Voucher_date'),
              operate: 'RANGE',
              addclass: 'datetimerange',
              autocomplete: false,
              formatter: wef.TableFormatter.firstEntryOnly
            },
            {
              field: 'summary',
              title: __('Summary'),
              operate: 'LIKE',
              table: table,
              class: 'autocontent',
              formatter: wef.TableFormatter.firstEntryWithFormatter(Table.api.formatter.content)
            },
            {
              field: 'entry_summary',
              title: '分录摘要',
              operate: false,
              formatter: function(value, row, index) {
                return value || '';
              }
            },
            {
              field: 'subject_name',
              title: '科目',
              operate: false,
              formatter: function(value, row, index) {
                return value || '';
              }
            },
            {
              field: 'debit_amount',
              title: '借方金额',
              operate: false,
              align: 'right',
              formatter: function(value, row, index) {
                return value || '';
              }
            },
            {
              field: 'credit_amount',
              title: '贷方金额',
              operate: false,
              align: 'right',
              formatter: function(value, row, index) {
                return value || '';
              }
            },
            {
              field: 'attachment_files',
              title: __('Attachment_files'),
              operate: false,
              formatter: wef.TableFormatter.firstEntryWithFormatter(Table.api.formatter.files)
            },
            {
              field: 'status',
              title: __('Status'),
              searchList: {"0": "草稿", "1": "已审核", "2": "已记账"},
              formatter: wef.TableFormatter.firstEntryWithFormatter(Table.api.formatter.status)
            },
            {
              field: 'operate',
              title: __('Operate'),
              table: table,
              events: Table.api.events.operate,
              buttons: [
                {
                  name: 'detail',
                  text: '详情',
                  title: '查看详情',
                  classname: 'btn btn-xs btn-info btn-dialog',
                  icon: 'fa fa-eye',
                  url: 'wefinancial/voucher/detail/ids/{ids}',
                  visible: wef.TableFormatter.visibility.firstEntryOnly
                },
                {
                  name: 'edit',
                  text: '编辑',
                  title: '编辑',
                  classname: 'btn btn-xs btn-success btn-dialog',
                  icon: 'fa fa-pencil',
                  url: 'wefinancial/voucher/edit/ids/{ids}',
                  visible: wef.TableFormatter.visibility.firstEntryWithStatus('0')
                },
                {
                  name: 'audit',
                  text: '审核',
                  title: '审核',
                  classname: 'btn btn-xs btn-info btn-ajax',
                  icon: 'fa fa-check',
                  url: 'wefinancial/voucher/audit',
                  confirm: '确认要审核这张凭证吗？',
                  refresh: true,
                  visible: wef.TableFormatter.visibility.firstEntryWithStatus('0')
                },
                {
                  name: 'unaudit',
                  text: '反审核',
                  title: '反审核',
                  classname: 'btn btn-xs btn-warning btn-ajax',
                  icon: 'fa fa-undo',
                  url: 'wefinancial/voucher/unaudit',
                  confirm: '确认要反审核这张凭证吗？',
                  refresh: true,
                  visible: wef.TableFormatter.visibility.firstEntryWithStatus('1')
                },
                {
                  name: 'post',
                  text: '记账',
                  title: '记账',
                  classname: 'btn btn-xs btn-primary btn-ajax',
                  icon: 'fa fa-book',
                  url: 'wefinancial/voucher/post',
                  confirm: '确认要记账这张凭证吗？',
                  refresh: true,
                  visible: wef.TableFormatter.visibility.firstEntryWithStatus('1')
                },
                {
                  name: 'unpost',
                  text: '反记账',
                  title: '反记账',
                  classname: 'btn btn-xs btn-danger btn-ajax',
                  icon: 'fa fa-reply',
                  url: 'wefinancial/voucher/unpost',
                  confirm: '确认要反记账这张凭证吗？',
                  refresh: true,
                  visible: wef.TableFormatter.visibility.firstEntryWithStatus('2')
                },
                {
                  name: 'del',
                  text: '删除',
                  title: '删除',
                  classname: 'btn btn-xs btn-danger btn-ajax',
                  icon: 'fa fa-trash',
                  url: 'wefinancial/voucher/del',
                  confirm: '确认要删除这张凭证吗？',
                  refresh: true,
                  visible: wef.TableFormatter.visibility.firstEntryWithStatus('0')
                }
              ],
              formatter: Table.api.formatter.operate
            }
          ]
        ]
      });


      table.on('load-success.bs.table', function (data) {
        $(".btn-add").data("area", ["76%", "800px"]);
        $(".btn-editone").data("area", ["76%", "800px"]);
        $(".btn-edit").data("area", ["76%", "800px"]);
        $(".btn-dialog").data("area", ["76%", "800px"]);

        // 合并单元格
        Controller.api.mergeCells(table);

        // 设置凭证组hover效果
        Controller.api.setupVoucherGroupHover(table);

        // 设置凭证组选中效果
        Controller.api.setupVoucherGroupSelection(table);
      })

      // 为表格绑定事件
      Table.api.bindevent(table);
    },
    recyclebin: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          'dragsort_url': ''
        }
      });

      var table = $("#table");

      // 初始化表格
      table.bootstrapTable({
        url: 'wefinancial/voucher/recyclebin' + location.search,
        pk: 'id',
        sortName: 'id',
        columns: [
          [
            {checkbox: true},
            {field: 'id', title: __('Id')},
            {
              field: 'deletetime',
              title: __('Deletetime'),
              operate: 'RANGE',
              addclass: 'datetimerange',
              formatter: Table.api.formatter.datetime
            },
            {
              field: 'operate',
              width: '140px',
              title: __('Operate'),
              table: table,
              events: Table.api.events.operate,
              buttons: [
                {
                  name: 'Restore',
                  text: __('Restore'),
                  classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                  icon: 'fa fa-rotate-left',
                  url: 'wefinancial/voucher/restore',
                  refresh: true
                },
                {
                  name: 'Destroy',
                  text: __('Destroy'),
                  classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                  icon: 'fa fa-times',
                  url: 'wefinancial/voucher/destroy',
                  refresh: true
                }
              ],
              formatter: Table.api.formatter.operate
            }
          ]
        ]
      });

      // 为表格绑定事件
      Table.api.bindevent(table);
    },

    add: function () {
      Controller.api.bindevent();
      Controller.api.initEntries();
    },
    edit: function () {
      Controller.api.bindevent();
      Controller.api.initEntriesForEdit();
    },
    detail: function () {
      Controller.api.initVoucherDetail();
    },
    api: {
      bindevent: function () {
        Form.api.bindevent($("form[role=form]"));

        // 添加分录行 - 阻止默认行为，不发送请求
        $(document).on('click', '#add-entry', function (e) {
          e.preventDefault(); // 阻止默认的表单提交行为
          Controller.api.addEntryRow();
          return false; // 确保不会触发任何请求
        });

        // 删除分录行
        $(document).on('click', '.remove-entry', function () {
          $(this).closest('tr').remove();
          Controller.api.updateRowIndexes();
          Controller.api.calculateTotals();
        });

        // 金额输入时计算总额
        $(document).on('input', '.debit-amount, .credit-amount', function () {
          Controller.api.calculateTotals();
        });

        // 科目选择变化时加载辅助核算 - 使用selectpage的change事件
        $(document).on('change', '.subject-select', function () {
          var $this = $(this);
          var subjectId = $this.val();
          var $auxCell = $this.closest('tr').find('.aux-cell');

          console.log('科目选择变化, 科目ID:', subjectId);

          if (subjectId) {
            // 由于使用了selectpage并且设置了leaf_flag=1的过滤条件，
            // 这里不再需要检查叶子节点，直接加载辅助核算类型
            Controller.api.loadAuxiliaryTypes(subjectId, $auxCell);
          } else {
            $auxCell.html('<input type="hidden" name="' + $auxCell.data('field-name') + '" value=""><div class="aux-hint">请先选择科目</div>');
            $auxCell.closest('tr').removeClass('has-multiple-aux');
          }
        });

        // 表单提交前验证
        $("form[role=form]").on('submit', function (e) {
          if (!Controller.api.validateEntries()) {
            e.preventDefault();
            return false;
          }
        });

        // 处理不同提交按钮的点击事件
        $(document).on('click', 'button[type="submit"]', function (e) {
          var $form = $(this).closest('form');
          var action = $(this).val();

          // 移除之前可能存在的隐藏字段
          $form.find('input[name="action"]').remove();

          // 添加隐藏字段来标识点击的按钮
          $('<input>').attr({
            type: 'hidden',
            name: 'action',
            value: action
          }).appendTo($form);
        });
      },

      initEntries: function () {
        // 初始状态下直接渲染4条空白分录明细
        if ($('#entries-tbody tr').length === 0) {
          for (var i = 0; i < 2; i++) {
            Controller.api.addEntryRow();
          }
        }
      },

      initEntriesForEdit: function () {
        // 编辑时，先重新整理现有行的索引
        Controller.api.updateRowIndexes();

        // 初始化现有行的selectpage组件
        $('#entries-tbody tr').each(function() {
          var $row = $(this);
          Form.api.bindevent($row);
        });

        // 初始化现有行的辅助核算
        $('#entries-tbody tr').each(function() {
          var $row = $(this);
          var $auxCell = $row.find('.aux-cell');
          var subjectId = $auxCell.data('subject-id');
          var auxIds = $auxCell.data('aux-ids');

          console.log('初始化分录行辅助核算，科目ID:', subjectId, '辅助核算ID:', auxIds, '类型:', typeof auxIds);

          if (subjectId) {
            // 确保 auxIds 是字符串类型，处理 undefined 和 null 的情况
            var auxIdsStr = (auxIds === undefined || auxIds === null) ? '' : String(auxIds);
            Controller.api.loadAuxiliaryTypesForEdit(subjectId, $auxCell, auxIdsStr);
          }
        });

        // 计算现有分录的总额
        Controller.api.calculateTotals();

        // 如果现有分录少于2条，补充到2条
        var currentRows = $('#entries-tbody tr').length;
        if (currentRows < 2) {
          var needToAdd = 2 - currentRows;
          for (var i = 0; i < needToAdd; i++) {
            Controller.api.addEntryRow();
          }
          // 添加新行后再次更新索引
          Controller.api.updateRowIndexes();
        }
      },

      addEntryRow: function () {
        var index = $('#entries-tbody tr').length;

        var row = '<tr class="entry-row">' +
          '<td>' +
          '<input type="text" ' +
          'name="entries[' + index + '][subject_id]" ' +
          'class="form-control selectpage subject-select" ' +
          'data-source="wefinancial/subject/index" ' +
          'data-primary-key="id" ' +
          'data-field="name" ' +
          'data-order-by="code asc" ' +
          'data-params=\'{"status": 1, "leaf_flag": 1}\' ' +
          'data-format-item="{code} {name}" ' +
          'data-rule="required" ' +
          'placeholder="请选择科目">' +
          '</td>' +
          '<td>' +
          '<input type="text" name="entries[' + index + '][summary]" class="form-control" placeholder="摘要">' +
          '</td>' +
          '<td>' +
          '<input type="number" name="entries[' + index + '][debit_amount]" class="form-control debit-amount" step="0.01" min="0" placeholder="0.00">' +
          '</td>' +
          '<td>' +
          '<input type="number" name="entries[' + index + '][credit_amount]" class="form-control credit-amount" step="0.01" min="0" placeholder="0.00">' +
          '</td>' +
          '<td class="aux-cell" data-field-name="entries[' + index + '][aux_ids]">' +
          '<input type="hidden" name="entries[' + index + '][aux_ids]" value="">' +
          '<div class="aux-hint">请先选择科目</div>' +
          '</td>' +
          '<td style="vertical-align: middle">' +
          '<button type="button" class="btn btn-xs btn-danger remove-entry">' +
          '<i class="fa fa-trash"></i>' +
          '</button>' +
          '</td>' +
          '</tr>';

        $('#entries-tbody').append(row);

        // 初始化新添加行的selectpage组件
        var $newRow = $('#entries-tbody tr:last');
        var $selectpage = $newRow.find('.selectpage');
        if ($selectpage.length > 0) {
          Form.api.bindevent($newRow);
        }
      },



      updateRowIndexes: function () {
        $('#entries-tbody tr').each(function (index) {
          $(this).find('input, select').each(function () {
            var name = $(this).attr('name');
            if (name && name.indexOf('entries[') === 0) {
              // 更精确的替换，确保完全匹配 entries[数字] 模式
              var newName = name.replace(/^entries\[\d+\]/, 'entries[' + index + ']');
              $(this).attr('name', newName);
            }
          });

          // 更新辅助核算单元格的data-field-name属性
          $(this).find('.aux-cell').each(function() {
            $(this).attr('data-field-name', 'entries[' + index + '][aux_ids]');
          });
        });
      },

      calculateTotals: function () {
        var debitTotal = 0;
        var creditTotal = 0;

        $('.debit-amount').each(function () {
          var value = parseFloat($(this).val()) || 0;
          debitTotal += value;
        });

        $('.credit-amount').each(function () {
          var value = parseFloat($(this).val()) || 0;
          creditTotal += value;
        });

        $('#debit-total').text(debitTotal.toFixed(2));
        $('#credit-total').text(creditTotal.toFixed(2));

        // 检查借贷平衡
        var balanced = Math.abs(debitTotal - creditTotal) < 0.01;
        var statusElement = $('#balance-status');

        if (balanced && debitTotal > 0) {
          statusElement.removeClass('label-warning label-danger').addClass('label-success').text('已平衡');
        } else if (debitTotal === 0 && creditTotal === 0) {
          statusElement.removeClass('label-success label-danger').addClass('label-warning').text('未平衡');
        } else {
          statusElement.removeClass('label-success label-warning').addClass('label-danger').text('不平衡');
        }

        return balanced;
      },

      validateEntries: function () {
        var hasEntries = $('#entries-tbody tr').length > 0;
        if (!hasEntries) {
          Toastr.error('请至少添加一条分录');
          return false;
        }

        var hasValidEntry = false;
        var allSubjectsSelected = true;

        $('#entries-tbody tr').each(function () {
          var subjectId = $(this).find('.subject-select').val();
          var debitAmount = parseFloat($(this).find('.debit-amount').val()) || 0;
          var creditAmount = parseFloat($(this).find('.credit-amount').val()) || 0;

          if (!subjectId) {
            allSubjectsSelected = false;
            return false;
          }

          if (debitAmount > 0 || creditAmount > 0) {
            hasValidEntry = true;
          }
        });

        if (!allSubjectsSelected) {
          Toastr.error('请为所有分录选择科目');
          return false;
        }

        if (!hasValidEntry) {
          Toastr.error('请至少输入一条有效的分录金额');
          return false;
        }

        if (!Controller.api.calculateTotals()) {
          Toastr.error('借贷不平衡，请检查分录金额');
          return false;
        }

        return true;
      },

      // 加载科目的辅助核算类型
      loadAuxiliaryTypes: function(subjectId, $auxCell) {
        console.log('加载科目辅助核算类型, 科目ID:', subjectId);
        $.ajax({
          url: 'wefinancial/auxiliary_type/getSubjectAuxTypes',
          type: 'GET',
          data: { subject_id: subjectId },
          dataType: 'json',
          success: function(response) {
            console.log('辅助核算类型响应:', response);
            if (response.code === 1) {
              var auxTypes = response.data;
              if (auxTypes && auxTypes.length > 0) {
                console.log('找到辅助核算类型:', auxTypes);
                Controller.api.renderAuxiliarySelectors(auxTypes, $auxCell);
              } else {
                console.log('该科目无辅助核算类型');
                $auxCell.html('<input type="hidden" name="' + $auxCell.data('field-name') + '" value=""><div class="aux-hint">无辅助核算</div>');
                $auxCell.closest('tr').removeClass('has-multiple-aux');
              }
            } else {
              console.error('获取辅助核算类型失败:', response.msg);
              $auxCell.html('<input type="hidden" name="' + $auxCell.data('field-name') + '" value=""><div class="aux-hint text-danger">加载失败</div>');
            }
          },
          error: function(xhr, status, error) {
            console.error('获取辅助核算类型请求失败:', status, error);
            $auxCell.html('<input type="hidden" name="' + $auxCell.data('field-name') + '" value=""><div class="aux-hint text-danger">网络错误</div>');
          }
        });
      },

      // 渲染辅助核算选择器
      renderAuxiliarySelectors: function(auxTypes, $auxCell) {
        var fieldName = $auxCell.data('field-name');
        var containerClass = auxTypes.length === 1 ? 'single-aux' : 'multiple-aux';

        var html = '<input type="hidden" name="' + fieldName + '" value="" class="aux-ids-input">';
        html += '<div class="aux-selectors-container ' + containerClass + '">';

        auxTypes.forEach(function(auxType, index) {
          html += '<div class="aux-selector-group" data-aux-type-id="' + auxType.id + '">';
          html += '<select class="form-control  aux-selector" data-aux-type-id="' + auxType.id + '" title="' + auxType.name + '">';
          html += '<option value="">请选择' + auxType.name + '</option>';
          html += '</select>';
          html += '</div>';
        });

        html += '</div>';
        $auxCell.html(html);

        // 如果是多个辅助核算，为表格行添加CSS类
        if (auxTypes.length > 1) {
          $auxCell.closest('tr').addClass('has-multiple-aux');
        } else {
          $auxCell.closest('tr').removeClass('has-multiple-aux');
        }

        // 为每个辅助核算类型加载对应的辅助核算项
        auxTypes.forEach(function(auxType) {
          Controller.api.loadAuxiliaryItems(auxType.id, $auxCell.find('.aux-selector[data-aux-type-id="' + auxType.id + '"]'));
        });

        // 绑定选择变化事件
        $auxCell.find('.aux-selector').on('change', function() {
          Controller.api.updateAuxIds($auxCell);
        });
      },

      // 加载辅助核算项
      loadAuxiliaryItems: function(auxTypeId, $selector) {
        $.ajax({
          url: 'wefinancial/auxiliary/getAuxiliaryItems',
          type: 'GET',
          data: { aux_type_id: auxTypeId },
          dataType: 'json',
          success: function(response) {
            if (response.code === 1) {
              var auxiliaries = response.data;
              auxiliaries.forEach(function(aux) {
                var optionText = aux.code ? (aux.code + ' - ' + aux.name) : aux.name;
                $selector.append('<option value="' + aux.id + '">' + optionText + '</option>');
              });
            } else {
              console.error('获取辅助核算项失败:', response.msg);
            }
          },
          error: function() {
            console.error('获取辅助核算项请求失败');
          }
        });
      },

      // 更新辅助核算ID字段
      updateAuxIds: function($auxCell) {
        var auxIds = [];
        $auxCell.find('.aux-selector').each(function() {
          var value = $(this).val();
          if (value) {
            auxIds.push(value);
          }
        });
        $auxCell.find('.aux-ids-input').val(auxIds.join(','));
      },

      // 编辑页面专用：加载科目的辅助核算类型并设置已选值
      loadAuxiliaryTypesForEdit: function(subjectId, $auxCell, selectedAuxIds) {
        console.log('编辑页面加载辅助核算类型，科目ID:', subjectId, '已选辅助核算:', selectedAuxIds);
        $.ajax({
          url: 'wefinancial/auxiliary_type/getSubjectAuxTypes',
          type: 'GET',
          data: { subject_id: subjectId },
          dataType: 'json',
          success: function(response) {
            console.log('编辑页面辅助核算类型响应:', response);
            if (response.code === 1) {
              var auxTypes = response.data;
              if (auxTypes && auxTypes.length > 0) {
                Controller.api.renderAuxiliarySelectorsForEdit(auxTypes, $auxCell, selectedAuxIds);
              } else {
                $auxCell.html('<input type="hidden" name="' + $auxCell.data('field-name') + '" value=""><div class="aux-hint">无辅助核算</div>');
                $auxCell.closest('tr').removeClass('has-multiple-aux');
              }
            } else {
              console.error('获取辅助核算类型失败:', response.msg);
              $auxCell.html('<input type="hidden" name="' + $auxCell.data('field-name') + '" value=""><div class="aux-hint text-danger">加载失败</div>');
            }
          },
          error: function(xhr, status, error) {
            console.error('获取辅助核算类型请求失败:', status, error);
            $auxCell.html('<input type="hidden" name="' + $auxCell.data('field-name') + '" value=""><div class="aux-hint text-danger">网络错误</div>');
          }
        });
      },

      // 编辑页面专用：渲染辅助核算选择器并设置已选值
      renderAuxiliarySelectorsForEdit: function(auxTypes, $auxCell, selectedAuxIds) {
        console.log('渲染编辑页面辅助核算选择器，辅助核算类型:', auxTypes, '已选ID:', selectedAuxIds);

        var fieldName = $auxCell.data('field-name');
        // 确保 selectedAuxIds 是字符串类型，并且处理空值情况
        var selectedIdsStr = selectedAuxIds ? String(selectedAuxIds).trim() : '';
        var selectedIds = selectedIdsStr ? selectedIdsStr.split(',').map(function(id) { return id.trim(); }) : [];
        var containerClass = auxTypes.length === 1 ? 'single-aux' : 'multiple-aux';

        console.log('处理后的已选ID数组:', selectedIds);

        var html = '<input type="hidden" name="' + fieldName + '" value="' + selectedIdsStr + '" class="aux-ids-input">';
        html += '<div class="aux-selectors-container ' + containerClass + '">';

        auxTypes.forEach(function(auxType, index) {
          html += '<div class="aux-selector-group" data-aux-type-id="' + auxType.id + '">';
          html += '<select class="form-control  aux-selector" data-aux-type-id="' + auxType.id + '" title="' + auxType.name + '">';
          html += '<option value="">请选择' + auxType.name + '</option>';
          html += '</select>';
          html += '</div>';
        });

        html += '</div>';
        $auxCell.html(html);

        // 如果是多个辅助核算，为表格行添加CSS类
        if (auxTypes.length > 1) {
          $auxCell.closest('tr').addClass('has-multiple-aux');
        } else {
          $auxCell.closest('tr').removeClass('has-multiple-aux');
        }

        // 为每个辅助核算类型加载对应的辅助核算项，并设置已选值
        var loadPromises = [];
        auxTypes.forEach(function(auxType) {
          var $selector = $auxCell.find('.aux-selector[data-aux-type-id="' + auxType.id + '"]');
          var promise = Controller.api.loadAuxiliaryItemsForEdit(auxType.id, $selector, selectedIds);
          loadPromises.push(promise);
        });

        // 等待所有辅助核算项加载完成后绑定事件
        Promise.all(loadPromises).then(function() {
          console.log('所有辅助核算项加载完成，绑定事件');
          $auxCell.find('.aux-selector').on('change', function() {
            Controller.api.updateAuxIds($auxCell);
          });
        }).catch(function(error) {
          console.error('加载辅助核算项时出错:', error);
        });
      },

      // 编辑页面专用：加载辅助核算项并设置已选值
      loadAuxiliaryItemsForEdit: function(auxTypeId, $selector, selectedIds) {
        console.log('加载辅助核算项，类型ID:', auxTypeId, '已选ID:', selectedIds);
        return new Promise(function(resolve, reject) {
          $.ajax({
            url: 'wefinancial/auxiliary/getAuxiliaryItems',
            type: 'GET',
            data: { aux_type_id: auxTypeId },
            dataType: 'json',
            success: function(response) {
              console.log('辅助核算项响应:', response);
              if (response.code === 1) {
                var auxiliaries = response.data;
                auxiliaries.forEach(function(aux) {
                  var optionText = aux.code ? (aux.code + ' - ' + aux.name) : aux.name;
                  var isSelected = selectedIds.indexOf(aux.id.toString()) !== -1;
                  var option = '<option value="' + aux.id + '"' + (isSelected ? ' selected' : '') + '>' + optionText + '</option>';
                  $selector.append(option);
                  if (isSelected) {
                    console.log('设置选中项:', aux.name, aux.id);
                  }
                });
                resolve();
              } else {
                console.error('获取辅助核算项失败:', response.msg);
                reject();
              }
            },
            error: function(xhr, status, error) {
              console.error('获取辅助核算项请求失败:', status, error);
              reject();
            }
          });
        });
      },

      // 合并单元格
      mergeCells: function(table) {
        // 延迟执行，确保表格已完全渲染
        setTimeout(function() {
          var data = table.bootstrapTable('getData');
          if (!data || data.length === 0) return;

          // 需要合并的字段
          var mergeFields = ['checkbox', 'id','period', 'voucher_no', 'voucher_date', 'summary', 'attachment_files', 'status', 'operate'];

          // 统计每个凭证的分录数量和起始行号
          var voucherMap = {}; // { voucherId: { start: 行号, count: 数量 } }
          data.forEach(function(row, idx) {
            if (!voucherMap[row.id]) {
              voucherMap[row.id] = { start: idx, count: 1 };
            } else {
              voucherMap[row.id].count++;
            }
          });

          // 合并单元格
          Object.keys(voucherMap).forEach(function(voucherId) {
            var info = voucherMap[voucherId];
            if (info.count > 1) {
              mergeFields.forEach(function(field) {
                try {
                  table.bootstrapTable('mergeCells', {
                    index: info.start,
                    field: field,
                    rowspan: info.count,
                    colspan: 1
                  });
                } catch (e) {
                  console.warn('合并单元格失败:', field, e);
                }
              });
            }
          });
        }, 100);
      },

      // 设置凭证组hover效果
      setupVoucherGroupHover: function(table) {
        // 延迟执行，确保合并单元格已完成
        setTimeout(function() {
          var data = table.bootstrapTable('getData');
          if (!data || data.length === 0) return;

          // 统计每个凭证的分录数量和起始行号
          var voucherMap = {}; // { voucherId: { start: 行号, count: 数量 } }
          data.forEach(function(row, idx) {
            if (!voucherMap[row.id]) {
              voucherMap[row.id] = { start: idx, count: 1 };
            } else {
              voucherMap[row.id].count++;
            }
          });

          // 先移除所有现有的hover事件，避免重复绑定
          table.find('tbody tr').off('mouseenter.voucher mouseleave.voucher');

          // 为每个凭证的所有行添加标识类和hover事件
          var voucherIndex = 0;
          Object.keys(voucherMap).forEach(function(voucherId) {
            var info = voucherMap[voucherId];
            var $rows = table.find('tbody tr').slice(info.start, info.start + info.count);

            // 添加凭证组标识类
            $rows.addClass('voucher-group-row voucher-group-' + voucherId);

            // 添加斑马条纹类（奇数索引的凭证组添加条纹背景）
            if (voucherIndex % 2 === 0) {
              $rows.addClass('voucher-stripe-even');
            }

            // 使用命名空间绑定hover事件，避免与其他事件冲突
            $rows.on('mouseenter.voucher', function(e) {
              // 阻止事件冒泡，避免与Bootstrap默认hover冲突
              e.stopPropagation();
              $('.voucher-group-' + voucherId).addClass('voucher-hover');
            }).on('mouseleave.voucher', function(e) {
              e.stopPropagation();
              $('.voucher-group-' + voucherId).removeClass('voucher-hover');
            });

            voucherIndex++;
          });
        }, 150); // 比合并单元格稍晚执行
      },

      // 设置凭证组选中效果
      setupVoucherGroupSelection: function(table) {
        // 延迟执行，确保合并单元格和hover效果已完成
        setTimeout(function() {
          var data = table.bootstrapTable('getData');
          if (!data || data.length === 0) return;

          // 统计每个凭证的分录数量和起始行号
          var voucherMap = {}; // { voucherId: { start: 行号, count: 数量 } }
          data.forEach(function(row, idx) {
            if (!voucherMap[row.id]) {
              voucherMap[row.id] = { start: idx, count: 1 };
            } else {
              voucherMap[row.id].count++;
            }
          });

          // 监听checkbox选中事件
          table.on('check.bs.table uncheck.bs.table', function(e, row) {
            var voucherId = row.id;
            var info = voucherMap[voucherId];
            if (info) {
              var $rows = $('.voucher-group-' + voucherId);
              if (e.type === 'check') {
                $rows.addClass('voucher-selected');
              } else {
                $rows.removeClass('voucher-selected');
              }
            }
          });

          // 监听全选/全不选事件
          table.on('check-all.bs.table uncheck-all.bs.table', function(e, rows) {
            Object.keys(voucherMap).forEach(function(voucherId) {
              var $rows = $('.voucher-group-' + voucherId);
              if (e.type === 'check-all') {
                $rows.addClass('voucher-selected');
              } else {
                $rows.removeClass('voucher-selected');
              }
            });
          });
        }, 200); // 比hover效果稍晚执行
      },



      // 初始化凭证详情页面
      initVoucherDetail: function() {
        // 计算并显示合计
        Controller.api.calculateDetailTotals();
      },

      // 计算详情页面的借贷合计
      calculateDetailTotals: function() {
        var debitTotal = 0;
        var creditTotal = 0;

        $('#entries-tbody tr').each(function() {
          var $debitCell = $(this).find('td:eq(2) .text-danger');
          var $creditCell = $(this).find('td:eq(3) .text-success');

          if ($debitCell.length) {
            var debitText = $debitCell.text().replace(/,/g, '');
            debitTotal += parseFloat(debitText) || 0;
          }

          if ($creditCell.length) {
            var creditText = $creditCell.text().replace(/,/g, '');
            creditTotal += parseFloat(creditText) || 0;
          }
        });

        $('#debit-total').text(debitTotal.toFixed(2));
        $('#credit-total').text(creditTotal.toFixed(2));
      },




    }
  };
  return Controller;
});
