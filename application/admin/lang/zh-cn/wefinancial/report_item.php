<?php

return [
    'Id'                       => '主键ID',
    'Template_id'              => '模板ID',
    'Item_name'                => '项目名称',
    'Line_number'              => '行次',
    'Column_position'          => '列位置',
    'Column_position left'     => '左栏',
    'Column_position right'    => '右栏',
    'Column_position single'   => '单栏',
    'Type'                     => '项目类型',
    'Type header'              => '标题',
    'Type detail'              => '明细',
    'Type subtotal'            => '小计',
    'Type total'               => '合计',
    'Type section'             => '分组',
    'Calculation_type'         => '计算类型',
    'Calculation_type formula' => '公式计算',
    'Calculation_type  direct' => '直接取数',
    'Calculation_formula'      => '计算公式（当calculation_type为formula时使用）',
    'Weigh'                    => '显示顺序',
    'Bold'                     => '加粗显示',
    'Bold 1'                   => '是',
    'Bold 0'                   => '否',
    'Indent'                   => '缩进级别',
    'Status'                   => '状态',
    'Status 1'                 => '启用',
    'Set status to 1'          => '设为启用',
    'Status 0'                 => '禁用',
    'Set status to 0'          => '设为禁用',
    'Createtime'               => '创建时间',
    'Updatetime'               => '更新时间'
];
