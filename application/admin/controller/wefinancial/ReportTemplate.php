<?php

namespace app\admin\controller\wefinancial;

use app\common\controller\Backend;

/**
 * 报表模板
 *
 * @icon fa fa-circle-o
 */
class ReportTemplate extends Backend
{

    /**
     * ReportTemplate模型对象
     * @var \app\admin\model\wefinancial\ReportTemplate
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\wefinancial\ReportTemplate;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    public function add() {
        return;
    }
//编辑
    public function edit($ids = null) {
        return;
    }
//删除
    public function del($ids = null) {
        return;
    }
//批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }
}
