<?php

namespace app\admin\controller\wefinancial;

use app\admin\model\wefinancial\Subject;
use app\common\controller\Backend;

/**
 * 辅助核算类型
 *
 * @icon fa fa-circle-o
 */
class AuxiliaryType extends Backend
{
    protected $noNeedRight = ['getSubjectAuxTypes'];

    /**
     * AuxiliaryType模型对象
     * @var \app\admin\model\wefinancial\AuxiliaryType
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\wefinancial\AuxiliaryType;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 获取科目的辅助核算类型（包括继承自上级的）
     */
    public function getSubjectAuxTypes()
    {
        $subjectId = $this->request->get('subject_id');
        if (!$subjectId) {
            $this->error('科目ID不能为空');
        }

        $subject = Subject::where('id', $subjectId)->field('id')->find();
        if (!$subject) {
            $this->error('科目不存在');
        }

        $auxTypes = [];
        // 获取继承的辅助核算类型
        $auxTypeIds = Subject::getInheritedAuxTypes($subjectId);

        if (!empty($auxTypeIds)) {
            $auxTypes = \app\admin\model\wefinancial\AuxiliaryType::whereIn('id', $auxTypeIds)
                ->field('id,name')
                ->select();
        }

        $this->success('获取成功', null, $auxTypes);
    }
}
