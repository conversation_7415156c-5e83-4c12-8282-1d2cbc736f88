<?php

namespace app\admin\controller\wefinancial;

use app\common\controller\Backend;
use app\admin\model\wefinancial\Period;
use app\admin\model\wefinancial\Voucher;
use app\admin\model\wefinancial\PeriodBalance;
use think\Db;
use think\Exception;
use think\Log;

/**
 * 会计期间结账
 *
 * @icon fa fa-lock
 */
class Closing extends Backend
{
    /**
     * Period模型对象
     * @var Period
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\wefinancial\Period;
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            // 获取期间列表
            $periods = $this->model->order('period', 'desc')->select();

            $list = [];
            foreach ($periods as $period) {
                $item = $period->toArray();

                // 获取期间统计信息
                $stats = $this->getPeriodStats($period->period);
                $item = array_merge($item, $stats);

                // 判断是否可以结账/反结账
                $item['can_close'] = $this->canClosePeriod($period);
                $item['can_unclose'] = $this->canUnclosePeriod($period);

                $list[] = $item;
            }

            $result = [
                'total' => count($list),
                'rows' => $list
            ];

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 结账
     */
    public function close()
    {
        $period = $this->request->post('period');

        if (empty($period)) {
            $this->error('请选择要结账的期间');
        }

        try {
            $periodModel = $this->model->where('period', $period)->find();
            if (!$periodModel) {
                $this->error('期间不存在');
            }

            if ($periodModel->status == 1) {
                $this->error('该期间已经结账');
            }

            // 执行结账前检查
            $checkResult = $this->checkBeforeClose($period);
            if (!$checkResult['success']) {
                // 如果是损益结转问题，返回特殊响应
                if (isset($checkResult['data'])) {
                    $this->success($checkResult['message'], null, [
                        'need_profit_loss_check' => true,
                        'balances' => $checkResult['data']
                    ]);
                } else {
                    $this->error($checkResult['message']);
                }
            }

            Db::startTrans();
            try {
                // 执行结账
                $this->performClose($period);

                Db::commit();
                $this->success('结账成功');
            } catch (Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (Exception $e) {
            $this->error('结账失败：' . $e->getMessage());
        }
    }

    /**
     * 反结账
     */
    public function unclose()
    {
        $period = $this->request->post('period');

        if (empty($period)) {
            $this->error('请选择要反结账的期间');
        }

        try {
            $periodModel = $this->model->where('period', $period)->find();
            if (!$periodModel) {
                $this->error('期间不存在');
            }

            if ($periodModel->status == 0) {
                $this->error('该期间未结账');
            }

            // 执行反结账前检查
            $checkResult = $this->checkBeforeUnclose($period);
            if (!$checkResult['success']) {
                $this->error($checkResult['message']);
            }

            Db::startTrans();
            try {
                // 执行反结账
                $this->performUnclose($period);

                Db::commit();
                $this->success('反结账成功');
            } catch (Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (Exception $e) {
            $this->error('反结账失败：' . $e->getMessage());
        }
    }

    /**
     * 强制结账（忽略损益结转检查）
     */
    public function forceClose()
    {
        $period = $this->request->post('period');

        if (empty($period)) {
            $this->error('请选择要结账的期间');
        }

        try {
            $periodModel = $this->model->where('period', $period)->find();
            if (!$periodModel) {
                $this->error('期间不存在');
            }

            if ($periodModel->status == 1) {
                $this->error('该期间已经结账');
            }

            // 执行基本检查（不包括损益结转检查）
            $checkResult = $this->checkBasicBeforeClose($period);
            if (!$checkResult['success']) {
                $this->error($checkResult['message']);
            }

            Db::startTrans();
            try {
                // 执行结账
                $this->performClose($period);

                Db::commit();
                $this->success('结账成功');
            } catch (Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (Exception $e) {
            $this->error('结账失败：' . $e->getMessage());
        }
    }

//添加
    public function add() {
        return;
    }
//编辑
    public function edit($ids = null) {
        return;
    }
//删除
    public function del($ids = null) {
        return;
    }
//批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }

    /**
     * 获取期间统计信息
     */
    private function getPeriodStats($period)
    {
        $voucherModel = new Voucher;

        // 统计凭证数量
        $totalVouchers = $voucherModel->where('period', $period)->count();
        $draftVouchers = $voucherModel->where('period', $period)->where('status', 0)->count();
        $auditedVouchers = $voucherModel->where('period', $period)->where('status', 1)->count();
        $postedVouchers = $voucherModel->where('period', $period)->where('status', 2)->count();

        return [
            'total_vouchers' => $totalVouchers,
            'draft_vouchers' => $draftVouchers,
            'audited_vouchers' => $auditedVouchers,
            'posted_vouchers' => $postedVouchers,
        ];
    }

    /**
     * 检查是否可以结账
     */
    private function canClosePeriod($period)
    {
        // 已结账的不能再结账
        if ($period->status == 1) {
            return false;
        }

        // 检查上期是否已结账
        $previousPeriod = $this->getPreviousPeriod($period->period);
        if ($previousPeriod) {
            $prevPeriodModel = $this->model->where('period', $previousPeriod)->find();
            if ($prevPeriodModel && $prevPeriodModel->status == 0) {
                return false; // 上期未结账
            }
        }

        return true;
    }

    /**
     * 检查是否可以反结账
     */
    private function canUnclosePeriod($period)
    {
        // 未结账的不能反结账
        if ($period->status == 0) {
            return false;
        }

        // 检查下期是否已结账
        $nextPeriod = $this->getNextPeriod($period->period);
        if ($nextPeriod) {
            $nextPeriodModel = $this->model->where('period', $nextPeriod)->find();
            if ($nextPeriodModel && $nextPeriodModel->status == 1) {
                return false; // 下期已结账
            }
        }

        return true;
    }

    /**
     * 结账前检查
     */
    private function checkBeforeClose($period)
    {
        $voucherModel = new Voucher;

        // 1. 检查是否有未记账的凭证
        $unpostedCount = $voucherModel->where('period', $period)
            ->where('status', '<', 2)
            ->count();

        if ($unpostedCount > 0) {
            return [
                'success' => false,
                'message' => "存在 {$unpostedCount} 张未记账凭证，请先完成记账操作"
            ];
        }

        // 2. 检查上期是否已结账
        $previousPeriod = $this->getPreviousPeriod($period);
        if ($previousPeriod) {
            $prevPeriodModel = $this->model->where('period', $previousPeriod)->find();
            if ($prevPeriodModel && $prevPeriodModel->status == 0) {
                return [
                    'success' => false,
                    'message' => "上期 {$previousPeriod} 尚未结账，请先结账上期"
                ];
            }
        }

        // 3. 检查是否有未结转的损益
        $profitLossCheck = $this->checkUncarriedProfitLoss($period);
        if (!$profitLossCheck['success']) {
            return $profitLossCheck;
        }

        return ['success' => true];
    }

    /**
     * 反结账前检查
     */
    private function checkBeforeUnclose($period)
    {
        // 检查是否有晚于此期间的期间余额发生
        $balanceModel = new PeriodBalance;
        $laterPeriodBalances = $balanceModel->where('period', '>', $period)
            ->field('period')
            ->group('period')
            ->order('period', 'asc')
            ->select();

        if (!empty($laterPeriodBalances)) {
            $laterPeriods = array_column($laterPeriodBalances->toArray(), 'period');
            $periodsStr = implode('、', $laterPeriods);
            return [
                'success' => false,
                'message' => "存在晚于此期间的期间余额发生（期间：{$periodsStr}），不能反结账"
            ];
        }

        return ['success' => true];
    }

    /**
     * 执行结账
     */
    private function performClose($period)
    {
        // 1. 更新期间状态
        $this->model->where('period', $period)->update([
            'status' => 1,
            'remark' => '期间结账于 ' . date('Y-m-d H:i:s')
        ]);

        // 2. 创建下期期间记录
        $nextPeriod = $this->getNextPeriod($period);
        if ($nextPeriod) {
            $existingNext = $this->model->where('period', $nextPeriod)->find();
            if (!$existingNext) {
                $this->model->create([
                    'period' => $nextPeriod,
                    'status' => 0,
                    'remark' => '系统自动创建'
                ]);
            }
        }

        // 3. 结转期间余额（这里可以根据需要实现具体的结转逻辑）
//        $this->carryForwardBalance($period, $nextPeriod);
    }

    /**
     * 执行反结账
     */
    private function performUnclose($period)
    {
        // 更新期间状态
        $this->model->where('period', $period)->update([
            'status' => 0,
            'remark' => '期间反结账于 ' . date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 结转期间余额
     */
    private function carryForwardBalance($currentPeriod, $nextPeriod)
    {
        if (!$nextPeriod) {
            return;
        }

        $balanceModel = new PeriodBalance;

        // 获取当前期间的期末余额
        $currentBalances = $balanceModel->where('period', $currentPeriod)->select();

        foreach ($currentBalances as $balance) {
            // 检查下期是否已有余额记录
            $nextBalance = $balanceModel->where('subject_id', $balance->subject_id)
                ->where('period', $nextPeriod)
                ->find();

            if (!$nextBalance) {
                // 创建下期余额记录，期初余额 = 本期期末余额
                $balanceModel->create([
                    'subject_id' => $balance->subject_id,
                    'period' => $nextPeriod,
                    'begin_balance' => $balance->end_balance,
                    'begin_direction' => $balance->end_direction,
                    'debit_total' => 0.00,
                    'credit_total' => 0.00,
                    'end_balance' => $balance->end_balance,
                    'end_direction' => $balance->end_direction,
                ]);
            }
        }
    }

    /**
     * 获取上一期间
     */
    private function getPreviousPeriod($period)
    {
        $year = substr($period, 0, 4);
        $month = substr($period, 5, 2);

        $prevMonth = intval($month) - 1;
        if ($prevMonth == 0) {
            $prevMonth = 12;
            $year = intval($year) - 1;
        }

        return sprintf('%04d-%02d', $year, $prevMonth);
    }

    /**
     * 获取下一期间
     */
    private function getNextPeriod($period)
    {
        $year = substr($period, 0, 4);
        $month = substr($period, 5, 2);

        $nextMonth = intval($month) + 1;
        if ($nextMonth == 13) {
            $nextMonth = 1;
            $year = intval($year) + 1;
        }

        return sprintf('%04d-%02d', $year, $nextMonth);
    }

    /**
     * 检查未结转的损益
     */
    private function checkUncarriedProfitLoss($period)
    {
        $balanceModel = new PeriodBalance;

        // 获取损益类科目的期末余额
        $profitLossBalances = $this->getProfitLossBalances($period);

        if (empty($profitLossBalances)) {
            return ['success' => true];
        }

        // 检查是否有非零余额
        $uncarriedBalances = [];
        foreach ($profitLossBalances as $balance) {
            if (abs($balance['end_balance']) > 0.01) { // 大于1分钱认为有余额
                $uncarriedBalances[] = $balance;
            }
        }

        if (empty($uncarriedBalances)) {
            return ['success' => true];
        }

        // 有未结转的损益，返回详细信息
        return [
            'success' => false,
            'message' => '存在未结转的损益科目，请先进行损益结转',
            'data' => $uncarriedBalances
        ];
    }

    /**
     * 获取损益类科目的期末余额
     */
    private function getProfitLossBalances($period)
    {
        // 损益类科目类型名称（一级类型）
        $profitLossType = '损益';

        $balanceModel = new PeriodBalance;

        // 查询损益类科目的期末余额
        $balances = $balanceModel->alias('pb')
            ->join('fa_wefinancial_subject s', 'pb.subject_id = s.id')
            ->join('fa_wefinancial_subject_type st', 's.type_id = st.id')
            ->join('fa_wefinancial_subject_type st_parent', 'st.pid = st_parent.id', 'LEFT')
            ->where('pb.period', $period)
            ->where('st_parent.name', $profitLossType)
//            ->where('s.leaf_flag', 1) // 只查询末级科目
            ->where('s.status', 1) // 只查询启用的科目
            ->field('pb.*, s.code, s.name as subject_name, s.balance_direction, st.name as type_name, st_parent.name as parent_type_name')
            ->select();

        $result = [];
        foreach ($balances as $balance) {
            $result[] = [
                'subject_id' => $balance->subject_id,
                'subject_code' => $balance->code,
                'subject_name' => $balance->subject_name,
                'type_name' => $balance->parent_type_name ?: $balance->type_name,
                'balance_direction' => $balance->balance_direction,
                'end_balance' => $balance->end_balance,
                'end_direction' => $balance->end_direction,
                'period' => $balance->period
            ];
        }

        return $result;
    }

    /**
     * 基本结账前检查（不包括损益结转检查）
     */
    private function checkBasicBeforeClose($period)
    {
        $voucherModel = new Voucher;

        // 1. 检查是否有未记账的凭证
        $unpostedCount = $voucherModel->where('period', $period)
            ->where('status', '<', 2)
            ->count();

        if ($unpostedCount > 0) {
            return [
                'success' => false,
                'message' => "存在 {$unpostedCount} 张未记账凭证，请先完成记账操作"
            ];
        }

        // 2. 检查上期是否已结账
        $previousPeriod = $this->getPreviousPeriod($period);
        if ($previousPeriod) {
            $prevPeriodModel = $this->model->where('period', $previousPeriod)->find();
            if ($prevPeriodModel && $prevPeriodModel->status == 0) {
                return [
                    'success' => false,
                    'message' => "上期 {$previousPeriod} 尚未结账，请先结账上期"
                ];
            }
        }

        return ['success' => true];
    }
}
