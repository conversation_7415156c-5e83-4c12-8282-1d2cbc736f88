<?php

namespace app\admin\controller\wefinancial;

use app\admin\model\wefinancial\PeriodBalance;
use app\admin\model\wefinancial\Subject;
use app\common\controller\Backend;
use think\Db;

class InitialBalance extends Backend
{
    private $initialPeriod;

    /**
     * PeriodBalance模型对象
     * @var \app\admin\model\wefinancial\PeriodBalance
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $config = get_addon_config('wefinancial');
        $this->initialPeriod = $config['initial_period'];
        $this->model = new PeriodBalance;

        // 获取初始期间的状态信息
        $periodModel = new \app\admin\model\wefinancial\Period();
        $initialPeriodRecord = $periodModel->where('period', $this->initialPeriod)->find();
        $periodStatus = $initialPeriodRecord ? $initialPeriodRecord->status : 0;
        $periodStatusText = $initialPeriodRecord ? $initialPeriodRecord->status_text : '未结账';

        // 传递初始期间到视图
        $this->view->assign("initialPeriod", $this->initialPeriod);
        $this->view->assign("periodStatus", $periodStatus);
        $this->assignconfig('periodStatus', $periodStatus);
        $this->view->assign("periodStatusText", $periodStatusText);
        $this->assignconfig("beginDirectionList", $this->model->getBeginDirectionList());
    }

    public function index()
    {
        if ($this->request->isAjax()) {
            // 获取启用的科目列表（包含树形结构）
            $subjectModel = new Subject();
            $subjects = $subjectModel
                ->with('type')
                ->where('subject.status', 1)
                ->order('subject.code', 'asc')
                ->select();

            // 获取已存在的初始余额数据
            $existingBalances = $this->model
                ->where('period', $this->initialPeriod)
                ->column('*', 'subject_id');

            // 组装数据
            $list = [];
            foreach ($subjects as $subject) {
                $balance = $existingBalances[$subject->id] ?? null;

                $item = [
                    'id' => $subject->id,
                    'code' => $subject->code,
                    'name' => $subject->name,
                    'pid' => $subject->pid,
                    'level' => $subject->level,
                    'leaf_flag' => $subject->leaf_flag,
                    'balance_direction' => $subject->balance_direction,
                    'type' => $subject->type ? $subject->type->toArray() : null,
                    'begin_balance' => $balance ? $balance['begin_balance'] : '0.00',
                    'begin_direction' => $balance ? $balance['begin_direction'] : $subject->balance_direction,
                    'period_balance_id' => $balance ? $balance['id'] : null,
                ];

                $list[] = $item;
            }

            $result = array("total" => count($list), "rows" => $list);
            return json($result);
        }

        return $this->fetch();
    }

    /**
     * 保存初始余额
     */
    public function save()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }

        $params = $this->request->post();
        $balances = $params['balances'] ?? [];
        $forceUpdate = $params['force_update'] ?? false; // 是否强制更新

        if (empty($balances)) {
            $this->error('请至少输入一个科目的初始余额');
        }

        // 验证保存条件
        $validation = $this->model->validateInitialBalanceSave($this->initialPeriod, $balances);

        // 如果有错误，直接返回
        if (!$validation['can_save']) {
            $this->error('保存失败', null, $validation);
        }

        // 如果有警告且未强制更新，返回警告信息让用户确认
        if (!empty($validation['warnings']) && !$forceUpdate) {
            $this->error('需要确认', null, $validation);
        }

        Db::startTrans();
        try {
            foreach ($balances as $subjectId => $balanceData) {
                $beginBalance = floatval($balanceData['begin_balance'] ?? 0);
                $beginDirection = $balanceData['begin_direction'] ?? '借';

                // 跳过余额为0的科目
                if ($beginBalance == 0) {
                    continue;
                }

                // 检查是否已存在该科目的初始余额记录
                $existingBalance = $this->model
                    ->where('subject_id', $subjectId)
                    ->where('period', $this->initialPeriod)
                    ->find();

                $data = [
                    'subject_id' => $subjectId,
                    'period' => $this->initialPeriod,
                    'begin_balance' => $beginBalance,
                    'begin_direction' => $beginDirection,
                    'debit_total' => 0.00,
                    'credit_total' => 0.00,
                    'end_balance' => $beginBalance,
                    'end_direction' => $beginDirection,
                ];

                if ($existingBalance) {
                    // 更新现有记录
                    $existingBalance->save($data);
                } else {
                    // 创建新记录
                    $this->model->save($data);
                    $this->model = new PeriodBalance; // 重置模型实例
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('保存失败：' . $e->getMessage());
        }
        $this->success('初始余额保存成功');
    }

    /**
     * 试算平衡检查
     */
    public function trialBalance()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }

        $params = $this->request->post();
        $balances = $params['balances'] ?? [];

        $debitTotal = 0;
        $creditTotal = 0;

        foreach ($balances as $subjectId => $balanceData) {
            $beginBalance = floatval($balanceData['begin_balance'] ?? 0);
            $beginDirection = $balanceData['begin_direction'] ?? '借';

            if ($beginBalance > 0) {
                if ($beginDirection === '借') {
                    $debitTotal += $beginBalance;
                } else {
                    $creditTotal += $beginBalance;
                }
            }
        }

        $isBalanced = abs($debitTotal - $creditTotal) < 0.01; // 允许0.01的误差

        $result = [
            'debit_total' => number_format($debitTotal, 2),
            'credit_total' => number_format($creditTotal, 2),
            'difference' => number_format($debitTotal - $creditTotal, 2),
            'is_balanced' => $isBalanced,
            'message' => $isBalanced ? '试算平衡' : '试算不平衡，请检查录入数据'
        ];

        $this->success('试算完成', null, $result);
    }


//添加
    public function add() {
        return;
    }
//编辑
    public function edit($ids = null) {
        return;
    }
//删除
    public function del($ids = null) {
        return;
    }
//批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }
}
