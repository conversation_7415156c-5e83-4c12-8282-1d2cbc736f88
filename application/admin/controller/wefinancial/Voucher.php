<?php

namespace app\admin\controller\wefinancial;

use app\common\controller\Backend;
use app\admin\model\wefinancial\Subject;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 会计凭证
 *
 * @icon fa fa-circle-o
 */
class Voucher extends Backend
{

    /**
     * Voucher模型对象
     * @var \app\admin\model\wefinancial\Voucher
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\wefinancial\Voucher;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("voucherTypeList", $this->model->getVoucherTypeList());

        // 获取科目列表用于下拉选择（包含所有科目，前端会根据leaf_flag判断是否可选）
        $subjectModel = new Subject();
        $subjects = $subjectModel->where('status', 1)->field('id,code,name,aux_type_ids,leaf_flag')->order('code asc')->select();
        $this->assignconfig("subjectList", $subjects);
        $this->assign("subjectList", $subjects);
    }


    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();
        $list = $this->model
            ->with(['entries' => function($query) {
                $query->with('subject')->order('id', 'asc');
            }, 'createUser', 'auditUser', 'postUser'])
            ->where($where)
            ->order($sort, $order)
            ->paginate($limit);

        // 处理数据，将凭证分录展开为多行
        $rows = [];
        foreach ($list->items() as $voucher) {
            $entries = $voucher->entries;
            if (empty($entries)) {
                // 如果没有分录，显示一行空的分录数据
                $rows[] = array_merge($voucher->toArray(), [
                    'entry_summary' => '',
                    'subject_name' => '',
                    'debit_amount' => '',
                    'credit_amount' => '',
                    'is_first_entry' => true,
                    'entry_count' => 0
                ]);
            } else {
                foreach ($entries as $index => $entry) {
                    $rows[] = array_merge($voucher->toArray(), [
                        'entry_summary' => $entry->summary,
                        'subject_name' => $entry->subject ? $entry->subject->code . ' - ' . $entry->subject->name : '',
                        'debit_amount' => $entry->debit_amount > 0 ? number_format($entry->debit_amount, 2) : '',
                        'credit_amount' => $entry->credit_amount > 0 ? number_format($entry->credit_amount, 2) : '',
                        'is_first_entry' => $index === 0,
                        'entry_count' => count($entries)
                    ]);
                }
            }
        }

        $result = ['total' => $list->total(), 'rows' => $rows];
        return json($result);
    }

    /**
     * 添加
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            // 获取当前期间的第一天作为默认日期
            $periodModel = new \app\admin\model\wefinancial\Period();
            $defaultDate = $periodModel->getCurrentPeriodFirstDay();

            // 如果无法获取当前期间，则使用当前日期
            if (empty($defaultDate)) {
                $defaultDate = date('Y-m-d');
            }

            $this->view->assign('defaultDate', $defaultDate);
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');
        $entries = $this->request->post('entries/a', []);
        $action = $this->request->post('action', 'save');

        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }

        // 设置创建人
        $params['create_user_id'] = $this->auth->id;

        // 根据提交按钮设置状态
        if ($action === 'save_and_audit') {
            $params['status'] = '1';
            $params['audit_user_id'] = $this->auth->id;
            $params['audit_time'] = date('Y-m-d H:i:s');
        } else {
            $params['status'] = '0';
        }

        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }

            $result = $this->model->saveWithEntries($params, $entries);

        } catch (ValidateException|PDOException|\Exception $e) {
            $this->error($e->getMessage());
        }

        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }

        if (false === $this->request->isPost()) {
            // 检查凭证状态，只有草稿状态才能编辑
            if ($row->status != '0') {
                $this->error('只有草稿状态的凭证才能编辑');
            }

            // 获取凭证分录
            $entries = $row->entries;
            $this->view->assign('row', $row);
            $this->view->assign('entries', $entries);
            return $this->view->fetch();
        }

        // 再次检查凭证状态
        if ($row->status != '0') {
            $this->error('只有草稿状态的凭证才能编辑');
        }

        $params = $this->request->post('row/a');
        $entries = $this->request->post('entries/a', []);
        $action = $this->request->post('action', 'save');

        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $params = $this->preExcludeFields($params);

        // 根据提交按钮设置状态
        if ($action === 'save_and_audit') {
            $params['status'] = '1';
            $params['audit_user_id'] = $this->auth->id;
            $params['audit_time'] = date('Y-m-d H:i:s');
        } else {
            $params['status'] = '0';
        }

        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }

            $result = $row->updateWithEntries($params, $entries);

        } catch (ValidateException|PDOException|\Exception $e) {
            $this->error($e->getMessage());
        }

        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 查看详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->with(['createUser', 'auditUser', 'postUser'])->find($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }

        // 获取凭证分录，包含科目关联
        $entries = $row->entries()->with('subject')->select();
        $this->view->assign('row', $row);
        $this->view->assign('entries', $entries);
        $this->view->assign('readonly', true); // 标记为只读模式
        return $this->view->fetch();
    }

    /**
     * 审核凭证
     */
    public function audit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }

        try {
            $result = $row->audit($this->auth->id);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        if ($result === false) {
            $this->error('审核失败');
        }
        $this->success('审核成功');
    }

    /**
     * 反审核凭证
     */
    public function unaudit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }

        try {
            $result = $row->unaudit();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        if ($result === false) {
            $this->error('反审核失败');
        }
        $this->success('反审核成功');
    }

    /**
     * 记账凭证
     */
    public function post($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }

        try {
            $result = $row->post($this->auth->id);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        if ($result === false) {
            $this->error('记账失败');
        }
        $this->success('记账成功');
    }

    /**
     * 反记账凭证
     */
    public function unpost($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }

        try {
            $result = $row->unpost();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        if ($result === false) {
            $this->error('反记账失败');
        }
        $this->success('反记账成功');
    }

}
