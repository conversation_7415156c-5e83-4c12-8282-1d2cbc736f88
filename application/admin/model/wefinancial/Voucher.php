<?php

namespace app\admin\model\wefinancial;

use think\Log;
use think\Model;
use traits\model\SoftDelete;

class Voucher extends Model
{

    use SoftDelete;



    // 表名
    protected $name = 'wefinancial_voucher';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'voucher_type_text',
        'create_user_nickname',
        'audit_user_nickname',
        'post_user_nickname'
    ];



    public function getStatusList()
    {
        return ['0' => '草稿', '1' => '已审核', '2' => '已记账'];
    }

    public function getVoucherTypeList()
    {
        return ['记' => '记', '收' => '收', '付' => '付', '转' => '转'];
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    public function getVoucherTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['voucher_type'] ?? '');
        $list = $this->getVoucherTypeList();
        return $list[$value] ?? '';
    }

    public function getCreateUserNicknameAttr($value, $data)
    {
        return $this->createUser ? $this->createUser->nickname : '--';
    }

    public function getAuditUserNicknameAttr($value, $data)
    {
        return $this->auditUser ? $this->auditUser->nickname : '--';
    }

    public function getPostUserNicknameAttr($value, $data)
    {
        return $this->postUser ? $this->postUser->nickname : '--';
    }

    /**
     * 关联凭证分录
     */
    public function entries()
    {
        return $this->hasMany('VoucherEntry', 'voucher_id', 'id');
    }

    /**
     * 关联创建人
     */
    public function createUser()
    {
        return $this->belongsTo('app\admin\model\Admin', 'create_user_id', 'id');
    }

    /**
     * 关联审核人
     */
    public function auditUser()
    {
        return $this->belongsTo('app\admin\model\Admin', 'audit_user_id', 'id');
    }

    /**
     * 关联记账人
     */
    public function postUser()
    {
        return $this->belongsTo('app\admin\model\Admin', 'post_user_id', 'id');
    }

    /**
     * 保存凭证及其分录
     * @param array $data 凭证数据
     * @param array $entries 分录数据
     * @return bool
     * @throws \Exception
     */
    public function saveWithEntries($data, $entries = [])
    {
        \think\Db::startTrans();
        try {
            // 预处理数据
            $data = $this->preprocessVoucherData($data);

            // 保存凭证主表
            $result = $this->allowField(true)->save($data);
            if (!$result) {
                throw new \Exception('凭证保存失败');
            }

            // 如果有分录数据，保存分录
            if (!empty($entries)) {
                $this->saveEntries($entries);
            }

            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            throw $e;
        }
    }

    /**
     * 更新凭证及其分录
     * @param array $data 凭证数据
     * @param array $entries 分录数据
     * @return bool
     * @throws \Exception
     */
    public function updateWithEntries($data, $entries = [])
    {
        \think\Db::startTrans();
        try {
            // 预处理数据（编辑时不重新生成凭证编号）
            $data = $this->preprocessVoucherData($data, false);

            // 更新凭证主表
            $result = $this->allowField(true)->save($data);
            if ($result === false) {
                throw new \Exception('凭证更新失败');
            }

            // 删除原有分录
            VoucherEntry::where('voucher_id', $this->id)->delete();

            // 保存新分录
            if (!empty($entries)) {
                $this->saveEntries($entries);
            }

            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            throw $e;
        }
    }

    /**
     * 保存分录数据
     * @param array $entries
     * @throws \Exception
     */
    private function saveEntries($entries)
    {
        $entryModel = new VoucherEntry();
        $debitTotal = 0;
        $creditTotal = 0;

        foreach ($entries as $entry) {
            if (empty($entry['subject_id'])) {
                continue;
            }

            // 验证科目必须是叶子节点
            if (!Subject::isLeafSubject($entry['subject_id'])) {
                $subject = Subject::where('id', $entry['subject_id'])->field('code,name')->find();
                $subjectName = $subject ? $subject->code . ' - ' . $subject->name : '未知科目';
                throw new \Exception('科目【' . $subjectName . '】不是叶子节点，不能创建凭证分录');
            }

            $entry['voucher_id'] = $this->id;
            $entry['debit_amount'] = floatval($entry['debit_amount'] ?? 0);
            $entry['credit_amount'] = floatval($entry['credit_amount'] ?? 0);

            // 计算借贷总额
            $debitTotal += $entry['debit_amount'];
            $creditTotal += $entry['credit_amount'];

            // 保存分录
            $entryModel->allowField(true)->save($entry);
            $entryModel = new VoucherEntry(); // 重新实例化以保存下一条记录
        }

        // 验证借贷平衡
        if (abs($debitTotal - $creditTotal) > 0.01) {
            throw new \Exception('借贷不平衡，借方总额：' . $debitTotal . '，贷方总额：' . $creditTotal);
        }
    }

    /**
     * 预处理凭证数据
     * @param array $data 原始数据
     * @param bool $generateVoucherNo 是否生成凭证编号
     * @return array 处理后的数据
     * @throws \Exception
     */
    private function preprocessVoucherData($data, $generateVoucherNo = true)
    {
        // 根据记账日期自动生成会计期间
        if (!empty($data['voucher_date'])) {
            $data['period'] = date('Y-m', strtotime($data['voucher_date']));
        }

        // 自动生成凭证编号
        if ($generateVoucherNo && !empty($data['voucher_type']) && !empty($data['period'])) {
            $data['voucher_no'] = $this->generateVoucherNo($data['voucher_type'], $data['period']);
        }

        return $data;
    }

    /**
     * 生成凭证编号
     * @param string $voucherType 凭证字
     * @param string $period 会计期间
     * @return string 凭证编号
     * @throws \Exception
     */
    private function generateVoucherNo($voucherType, $period)
    {
        // 查询当期该凭证字的所有凭证编号
        $voucherNos = \think\Db::name('wefinancial_voucher')
            ->where('voucher_type', $voucherType)
            ->where('period', $period)
            ->where('deletetime', null)
            ->column('voucher_no');

        $maxSeq = 0;
        if (!empty($voucherNos)) {
            // 从所有凭证编号中提取序号部分，找出最大值
            $pattern = '/^' . preg_quote($voucherType, '/') . '-(\d+)$/';
            foreach ($voucherNos as $voucherNo) {
                if (preg_match($pattern, $voucherNo, $matches)) {
                    $seq = intval($matches[1]);
                    if ($seq > $maxSeq) {
                        $maxSeq = $seq;
                    }
                }
            }
        }

        $nextSeq = $maxSeq + 1;

        // 生成新的凭证编号，序号补零到4位
        return $voucherType . '-' . str_pad($nextSeq, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 审核凭证
     * @param int $userId 审核人ID
     * @return bool
     * @throws \Exception
     */
    public function audit($userId)
    {
        if ($this->status != '0') {
            throw new \Exception('只有草稿状态的凭证才能审核');
        }

        $result = $this->save([
            'status' => '1',
            'audit_user_id' => $userId,
            'audit_time' => date('Y-m-d H:i:s')
        ]);

        if (!$result) {
            throw new \Exception('审核失败');
        }

        return true;
    }

    /**
     * 反审核凭证
     * @return bool
     * @throws \Exception
     */
    public function unaudit()
    {
        if ($this->status == '2') {
            throw new \Exception('已记账的凭证不能反审核，请先反记账');
        }

        if ($this->status != '1') {
            throw new \Exception('只有已审核状态的凭证才能反审核');
        }

        $result = $this->save([
            'status' => '0',
            'audit_user_id' => null,
            'audit_time' => null
        ]);

        if (!$result) {
            throw new \Exception('反审核失败');
        }

        return true;
    }

    /**
     * 记账凭证
     * @param int $userId 记账人ID
     * @return bool
     * @throws \Exception
     */
    public function post($userId)
    {
        if ($this->status != '1') {
            throw new \Exception('只有已审核状态的凭证才能记账');
        }

        // 获取凭证分录
        $entries = VoucherEntry::where('voucher_id', $this->id)->select();
        if (empty($entries)) {
            throw new \Exception('凭证没有分录数据，无法记账');
        }

        // 检查历史期间记账限制
        $this->checkHistoricalPeriodRestriction($entries, $this->period, '记账');

        \think\Db::startTrans();
        try {
            // 更新凭证状态
            $result = $this->save([
                'status' => '2',
                'post_user_id' => $userId,
                'post_time' => date('Y-m-d H:i:s')
            ]);

            if (!$result) {
                throw new \Exception('记账失败');
            }

            // 更新科目期间余额
            $periodBalanceModel = new \app\admin\model\wefinancial\PeriodBalance();
            $periodBalanceModel->updateBalanceByVoucherEntries($entries, $this->period, false);

            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            throw $e;
        }
    }

    /**
     * 反记账凭证
     * @return bool
     * @throws \Exception
     */
    public function unpost()
    {
        if ($this->status != '2') {
            throw new \Exception('只有已记账状态的凭证才能反记账');
        }

        // 获取凭证分录（在更新状态前获取）
        $entries = VoucherEntry::where('voucher_id', $this->id)->select();
        if (empty($entries)) {
            throw new \Exception('凭证没有分录数据，无法反记账');
        }

        // 检查历史期间反记账限制
        $this->checkHistoricalPeriodRestriction($entries, $this->period, '反记账');

        \think\Db::startTrans();
        try {
            // 更新凭证状态
            $result = $this->save([
                'status' => '1',
                'post_user_id' => null,
                'post_time' => null
            ]);

            if (!$result) {
                throw new \Exception('反记账失败');
            }

            // 反向更新科目期间余额
            $periodBalanceModel = new \app\admin\model\wefinancial\PeriodBalance();
            $periodBalanceModel->updateBalanceByVoucherEntries($entries, $this->period, true);

            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            throw $e;
        }
    }

    /**
     * 检查历史期间记账/反记账限制
     * @param array $entries 凭证分录
     * @param string $voucherPeriod 凭证期间
     * @param string $operation 操作类型（记账/反记账）
     * @throws \Exception
     */
    private function checkHistoricalPeriodRestriction($entries, $voucherPeriod, $operation)
    {
        // 首先检查凭证期间是否已经结账
        $periodModel = new \app\admin\model\wefinancial\Period();
        $periodRecord = $periodModel->where('period', $voucherPeriod)->find();

        if ($periodRecord && $periodRecord->status == 1) {
            throw new \Exception("不允许对已结账期间（{$voucherPeriod}）进行{$operation}操作。\n\n已结账的期间不允许进行任何记账或反记账操作，请联系系统管理员。");
        }

        $periodBalanceModel = new \app\admin\model\wefinancial\PeriodBalance();
        $currentPeriod = $periodBalanceModel->getCurrentPeriod();

        // 如果凭证期间不是历史期间，则不需要进行后续业务检查
        if ($voucherPeriod >= $currentPeriod) {
            return;
        }

        // 提取凭证分录中涉及的科目ID
        $subjectIds = [];
        foreach ($entries as $entry) {
            if (!in_array($entry->subject_id, $subjectIds)) {
                $subjectIds[] = $entry->subject_id;
            }
        }

        // 检查这些科目在凭证期间之后是否有业务发生或余额变动
        $activityCheck = $periodBalanceModel->checkSubsequentPeriodActivity($subjectIds, $voucherPeriod);

        if ($activityCheck['has_activity']) {
            // 构建详细的错误信息
            $errorMessage = "不允许对历史期间（{$voucherPeriod}）进行{$operation}操作，因为相关科目在后续期间有业务发生或余额变动：\n\n";

            $groupedDetails = [];
            foreach ($activityCheck['details'] as $detail) {
                $key = $detail['subject_code'] . ' - ' . $detail['subject_name'];
                if (!isset($groupedDetails[$key])) {
                    $groupedDetails[$key] = [];
                }
                $groupedDetails[$key][] = $detail;
            }

            foreach ($groupedDetails as $subjectInfo => $details) {
                $errorMessage .= "【{$subjectInfo}】\n";
                foreach ($details as $detail) {
                    if ($detail['type'] === 'balance_activity') {
                        $errorMessage .= "  - {$detail['period']}期间有发生额（借方：{$detail['debit_total']}，贷方：{$detail['credit_total']}）\n";
                    } elseif ($detail['type'] === 'voucher_activity') {
                        $errorMessage .= "  - {$detail['period']}期间有已记账凭证：{$detail['voucher_no']}\n";
                    }
                }
                $errorMessage .= "\n";
            }

            $errorMessage .= "请先处理后续期间的相关业务，或联系系统管理员。";

            throw new \Exception($errorMessage);
        }
    }


}
