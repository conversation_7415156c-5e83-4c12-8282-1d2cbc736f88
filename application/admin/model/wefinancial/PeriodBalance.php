<?php

namespace app\admin\model\wefinancial;

use think\Model;
use traits\model\SoftDelete;

class PeriodBalance extends Model
{

    use SoftDelete;



    // 表名
    protected $name = 'wefinancial_period_balance';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'begin_direction_text',
        'end_direction_text'
    ];



    public function getBeginDirectionList()
    {
        return ['借' => __('借'), '贷' => __('贷')];
    }

    public function getEndDirectionList()
    {
        return ['借' => __('借'), '贷' => __('贷')];
    }


    public function getBeginDirectionTextAttr($value, $data)
    {
        $value = $value ?: ($data['begin_direction'] ?? '');
        $list = $this->getBeginDirectionList();
        return $list[$value] ?? '';
    }


    public function getEndDirectionTextAttr($value, $data)
    {
        $value = $value ?: ($data['end_direction'] ?? '');
        $list = $this->getEndDirectionList();
        return $list[$value] ?? '';
    }

    /**
     * 关联科目
     */
    public function subject()
    {
        return $this->belongsTo('Subject', 'subject_id', 'id');
    }

    /**
     * 批量创建或更新初始余额
     * @param string $period 会计期间
     * @param array $balances 余额数据 [subject_id => ['begin_balance' => amount, 'begin_direction' => direction]]
     * @return bool
     * @throws \Exception
     */
    public function batchCreateOrUpdateInitialBalance($period, $balances)
    {
        \think\Db::startTrans();
        try {
            foreach ($balances as $subjectId => $balanceData) {
                $beginBalance = floatval($balanceData['begin_balance'] ?? 0);
                $beginDirection = $balanceData['begin_direction'] ?? '借';

                // 跳过余额为0的科目
                if ($beginBalance == 0) {
                    continue;
                }

                // 检查是否已存在该科目的余额记录
                $existingBalance = self::where('subject_id', $subjectId)
                    ->where('period', $period)
                    ->find();

                $data = [
                    'subject_id' => $subjectId,
                    'period' => $period,
                    'begin_balance' => $beginBalance,
                    'begin_direction' => $beginDirection,
                    'debit_total' => 0.00,
                    'credit_total' => 0.00,
                    'end_balance' => $beginBalance,
                    'end_direction' => $beginDirection,
                ];

                if ($existingBalance) {
                    // 更新现有记录
                    $existingBalance->save($data);
                } else {
                    // 创建新记录
                    $newBalance = new self();
                    $newBalance->save($data);
                }
            }

            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            throw $e;
        }
    }

    /**
     * 获取指定期间的初始余额数据
     * @param string $period 会计期间
     * @return array
     */
    public function getInitialBalanceByPeriod($period)
    {
        return $this->where('period', $period)
            ->with(['subject'])
            ->select();
    }

    /**
     * 计算试算平衡
     * @param array $balances 余额数据
     * @return array
     */
    public function calculateTrialBalance($balances)
    {
        $debitTotal = 0;
        $creditTotal = 0;

        foreach ($balances as $subjectId => $balanceData) {
            $beginBalance = floatval($balanceData['begin_balance'] ?? 0);
            $beginDirection = $balanceData['begin_direction'] ?? '借';

            if ($beginBalance > 0) {
                if ($beginDirection === '借') {
                    $debitTotal += $beginBalance;
                } else {
                    $creditTotal += $beginBalance;
                }
            }
        }

        $isBalanced = abs($debitTotal - $creditTotal) < 0.01; // 允许0.01的误差

        return [
            'debit_total' => $debitTotal,
            'credit_total' => $creditTotal,
            'difference' => $debitTotal - $creditTotal,
            'is_balanced' => $isBalanced,
        ];
    }

    /**
     * 检查是否已有非初始期间的记账记录
     * @param string $initialPeriod 初始期间
     * @return array
     */
    public function checkExistingRecords($initialPeriod)
    {
        // 检查是否存在非初始期间的余额记录
        $nonInitialRecords = $this->where('period', '<>', $initialPeriod)
            ->where('debit_total', '>', 0)
            ->whereOr('credit_total', '>', 0)
            ->count();

        // 检查是否存在已记账的凭证
        $postedVouchers = \think\Db::name('wefinancial_voucher')
            ->where('status', 2) // 已记账状态
            ->count();

        return [
            'has_non_initial_records' => $nonInitialRecords > 0,
            'has_posted_vouchers' => $postedVouchers > 0,
            'non_initial_count' => $nonInitialRecords,
            'posted_voucher_count' => $postedVouchers,
        ];
    }

    /**
     * 验证初始余额保存条件
     * @param string $initialPeriod 初始期间
     * @param array $balances 余额数据
     * @return array
     */
    public function validateInitialBalanceSave($initialPeriod, $balances)
    {
        $result = [
            'can_save' => true,
            'warnings' => [],
            'errors' => [],
        ];

        // 1. 检查初始期间是否已结账
        $periodModel = new \app\admin\model\wefinancial\Period();
        $initialPeriodRecord = $periodModel->where('period', $initialPeriod)->find();
        if ($initialPeriodRecord && $initialPeriodRecord->status == 1) {
            $result['can_save'] = false;
            $result['errors'][] = [
                'type' => 'period_closed_error',
                'message' => '初始会计期间已结账，不允许设置初始余额',
                'period' => $initialPeriod,
            ];
        }

        // 2. 检查借贷平衡
        $trialBalance = $this->calculateTrialBalance($balances);
        if (!$trialBalance['is_balanced']) {
            $result['can_save'] = false;
            $result['errors'][] = [
                'type' => 'balance_error',
                'message' => '初始余额借贷不平衡，不允许保存',
                'debit_total' => number_format($trialBalance['debit_total'], 2),
                'credit_total' => number_format($trialBalance['credit_total'], 2),
                'difference' => number_format($trialBalance['difference'], 2),
            ];
        }

        // 3. 检查是否已有记账记录
        $existingRecords = $this->checkExistingRecords($initialPeriod);
        if ($existingRecords['has_non_initial_records'] || $existingRecords['has_posted_vouchers']) {
            $result['warnings'][] = [
                'type' => 'existing_records_warning',
                'message' => '系统中已存在记账记录，修改初始余额可能影响财务数据的准确性',
                'details' => [
                    'non_initial_records' => $existingRecords['non_initial_count'],
                    'posted_vouchers' => $existingRecords['posted_voucher_count'],
                ],
            ];
        }

        return $result;
    }

    /**
     * 根据凭证分录更新期间余额
     * @param array $entries 凭证分录数据
     * @param string $period 会计期间
     * @param bool $isReverse 是否为反向操作（反记账）
     * @return bool
     * @throws \Exception
     */
    public function updateBalanceByVoucherEntries($entries, $period, $isReverse = false)
    {
        \think\Db::startTrans();
        try {
            $updatedSubjectIds = []; // 记录更新过的科目ID

            foreach ($entries as $entry) {
                $subjectId = $entry['subject_id'];
                $debitAmount = floatval($entry['debit_amount'] ?? 0);
                $creditAmount = floatval($entry['credit_amount'] ?? 0);

                // 跳过金额为0的分录
                if ($debitAmount == 0 && $creditAmount == 0) {
                    continue;
                }

                // 获取或创建期间余额记录
                $balance = $this->getOrCreatePeriodBalance($subjectId, $period);

                // 获取科目信息以确定科目方向
                $subject = \app\admin\model\wefinancial\Subject::find($subjectId);
                if (!$subject) {
                    throw new \Exception("科目ID {$subjectId} 不存在");
                }

                // 更新发生额
                if ($isReverse) {
                    // 反记账：减去发生额
                    $balance->debit_total -= $debitAmount;
                    $balance->credit_total -= $creditAmount;
                } else {
                    // 记账：增加发生额
                    $balance->debit_total += $debitAmount;
                    $balance->credit_total += $creditAmount;
                }

                // 重新计算期末余额和方向
                $this->calculateEndBalance($balance, $subject->balance_direction);

                // 保存更新后的余额
                $balance->save();

                // 记录更新过的科目ID
                $updatedSubjectIds[] = $subjectId;
            }

            // 检查并清理无业务发生的期间余额记录
            $this->cleanupEmptyPeriodBalances(array_unique($updatedSubjectIds), $period);

            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            throw $e;
        }
    }

    /**
     * 获取或创建期间余额记录
     * @param int $subjectId 科目ID
     * @param string $period 会计期间
     * @return PeriodBalance
     */
    public function getOrCreatePeriodBalance($subjectId, $period)
    {
        $balance = self::where('subject_id', $subjectId)
            ->where('period', $period)
            ->find();

        if (!$balance) {
            // 如果不存在，创建新的余额记录
            $subject = \app\admin\model\wefinancial\Subject::find($subjectId);

            // 获取上一期间的期末余额作为当前期间的期初余额
            $previousBalance = $this->getPreviousPeriodEndBalance($subjectId, $period);

            $balance = new self();
            $balance->subject_id = $subjectId;
            $balance->period = $period;
            $balance->begin_balance = $previousBalance['end_balance'];
            $balance->begin_direction = $previousBalance['end_direction'];
            $balance->debit_total = 0.00;
            $balance->credit_total = 0.00;
            $balance->end_balance = $previousBalance['end_balance'];
            $balance->end_direction = $previousBalance['end_direction'];
            $balance->save();
        }

        return $balance;
    }

    /**
     * 清理无业务发生的期间余额记录
     * @param array $subjectIds 科目ID数组
     * @param string $period 会计期间
     * @return void
     */
    private function cleanupEmptyPeriodBalances($subjectIds, $period)
    {
        foreach ($subjectIds as $subjectId) {
            // 获取当前期间余额记录
            $balance = self::where('subject_id', $subjectId)
                ->where('period', $period)
                ->find();

            if (!$balance) {
                continue;
            }

            // 检查是否有业务发生（借贷发生额都为0）
            $hasActivity = ($balance->debit_total > 0 || $balance->credit_total > 0);


            // 双重检查：确认该科目在该期间确实没有已记账的凭证分录
            $hasPostedEntries = $this->hasPostedEntriesInPeriod($subjectId, $period);

            // 如果没有业务发生且确实没有已记账分录，则删除该记录
            if (!$hasActivity &&  !$hasPostedEntries) {
                $balance->delete();
            }
        }
    }

    /**
     * 检查科目在指定期间是否有已记账的凭证分录
     * @param int $subjectId 科目ID
     * @param string $period 会计期间
     * @return bool
     */
    private function hasPostedEntriesInPeriod($subjectId, $period)
    {
        $count = \think\Db::name('wefinancial_voucher')
            ->alias('v')
            ->join('wefinancial_voucher_entry e', 'v.id = e.voucher_id')
            ->where('v.period', $period)
            ->where('v.status', 2) // 已记账状态
            ->where('e.subject_id', $subjectId)
            ->where('v.deletetime', null)
            ->where('e.deletetime', null)
            ->count();

        return $count > 0;
    }

    /**
     * 获取距离当期最近的往期期末余额
     * @param int $subjectId 科目ID
     * @param string $currentPeriod 当前期间
     * @return array
     */
    private function getPreviousPeriodEndBalance($subjectId, $currentPeriod)
    {
        // 查找距离当期最近的往期会计期间记录
        $previousBalance = self::where('subject_id', $subjectId)
            ->where('period', '<', $currentPeriod)
            ->order('period', 'desc')
            ->find();

        if ($previousBalance) {
            return [
                'end_balance' => $previousBalance->end_balance,
                'end_direction' => $previousBalance->end_direction
            ];
        }

        // 如果没有任何历史记录，使用科目默认方向和0余额
        $subject = \app\admin\model\wefinancial\Subject::find($subjectId);
        return [
            'end_balance' => 0.00,
            'end_direction' => $subject->balance_direction
        ];
    }

    /**
     * 计算上一期间
     * @param string $currentPeriod 当前期间 (格式: YYYY-MM)
     * @return string 上一期间
     */
    private function calculatePreviousPeriod($currentPeriod)
    {
        list($year, $month) = explode('-', $currentPeriod);
        $year = intval($year);
        $month = intval($month);

        $month--;
        if ($month < 1) {
            $month = 12;
            $year--;
        }

        return sprintf('%04d-%02d', $year, $month);
    }

    /**
     * 计算期末余额和方向
     * @param PeriodBalance $balance 余额记录
     * @param string $subjectDirection 科目方向
     */
    private function calculateEndBalance($balance, $subjectDirection)
    {
        $beginBalance = floatval($balance->begin_balance);
        $beginDirection = $balance->begin_direction;
        $debitTotal = floatval($balance->debit_total);
        $creditTotal = floatval($balance->credit_total);

        // 将期初余额转换为带符号的数值（借方为正，贷方为负）
        $beginBalanceSigned = ($beginDirection === '借') ? $beginBalance : -$beginBalance;

        // 计算期末余额（借方发生额为正，贷方发生额为负）
        $endBalanceSigned = $beginBalanceSigned + $debitTotal - $creditTotal;

        // 底层存储允许有符号的余额，保持科目余额方向不变
        $balance->end_balance = $endBalanceSigned;
        $balance->end_direction = $subjectDirection;

        // 确定期末余额的绝对值和方向，科目余额方向可变，保持余额为正数记录的方案(该方案不启用)
//        if ($endBalanceSigned > 0) {
//            $balance->end_balance = $endBalanceSigned;
//            $balance->end_direction = '借';
//        } elseif ($endBalanceSigned < 0) {
//            $balance->end_balance = abs($endBalanceSigned);
//            $balance->end_direction = '贷';
//        } else {
//            $balance->end_balance = 0.00;
//            $balance->end_direction = $subjectDirection; // 余额为0时保持科目默认方向
//        }
    }

    /**
     * 获取科目在指定期间及之前的最近余额记录
     * @param int $subjectId 科目ID
     * @param string $period 会计期间
     * @return PeriodBalance|null
     */
    public function getLatestBalanceBeforePeriod($subjectId, $period)
    {
        return self::where('subject_id', $subjectId)
            ->where('period', '<=', $period)
            ->order('period', 'desc')
            ->find();
    }

    /**
     * 获取期间余额列表（仅返回实际存在的记录）
     * @param string $period 会计期间
     * @return array
     */
    public function getPeriodBalanceList($period)
    {
        return $this->where('period', $period)
            ->with(['subject'])
            ->order('subject_id')
            ->select();
    }

    /**
     * 获取科目的历史余额轨迹
     * @param int $subjectId 科目ID
     * @param string $startPeriod 开始期间
     * @param string $endPeriod 结束期间
     * @return array
     */
    public function getSubjectBalanceHistory($subjectId, $startPeriod, $endPeriod)
    {
        return self::where('subject_id', $subjectId)
            ->where('period', '>=', $startPeriod)
            ->where('period', '<=', $endPeriod)
            ->order('period', 'asc')
            ->select();
    }

    // 静态缓存，避免重复查询
    private static $virtualBalanceCache = [];
    private static $subjectCache = [];

    /**
     * 获取科目在指定期间的虚拟余额（如果当期无记录，则基于最近历史记录计算）
     * @param int $subjectId 科目ID
     * @param string $period 会计期间
     * @return array
     */
    public function getVirtualBalance($subjectId, $period)
    {
        // 检查缓存
        $cacheKey = $subjectId . '_' . $period;
        if (isset(self::$virtualBalanceCache[$cacheKey])) {
            return self::$virtualBalanceCache[$cacheKey];
        }

        // 首先查找当期是否有记录
        $currentBalance = self::where('subject_id', $subjectId)
            ->where('period', $period)
            ->find();

        if ($currentBalance) {
            // 如果当期有记录，直接返回
            $result = [
                'subject_id' => $subjectId,
                'period' => $period,
                'begin_balance' => $currentBalance->begin_balance,
                'begin_direction' => $currentBalance->begin_direction,
                'debit_total' => $currentBalance->debit_total,
                'credit_total' => $currentBalance->credit_total,
                'end_balance' => $currentBalance->end_balance,
                'end_direction' => $currentBalance->end_direction,
                'has_actual_record' => true
            ];
            self::$virtualBalanceCache[$cacheKey] = $result;
            return $result;
        }

        // 如果当期无记录，查找最近的历史记录
        $latestBalance = $this->getLatestBalanceBeforePeriod($subjectId, $period);

        if ($latestBalance) {
            // 基于最近历史记录构造虚拟余额
            $result = [
                'subject_id' => $subjectId,
                'period' => $period,
                'begin_balance' => $latestBalance->end_balance,
                'begin_direction' => $latestBalance->end_direction,
                'debit_total' => 0.00,
                'credit_total' => 0.00,
                'end_balance' => $latestBalance->end_balance,
                'end_direction' => $latestBalance->end_direction,
                'has_actual_record' => false,
                'based_on_period' => $latestBalance->period
            ];
            self::$virtualBalanceCache[$cacheKey] = $result;
            return $result;
        }

        // 如果没有任何历史记录，返回零余额
        $subject = $this->getSubjectFromCache($subjectId);
        $result = [
            'subject_id' => $subjectId,
            'period' => $period,
            'begin_balance' => 0.00,
            'begin_direction' => $subject->balance_direction,
            'debit_total' => 0.00,
            'credit_total' => 0.00,
            'end_balance' => 0.00,
            'end_direction' => $subject->balance_direction,
            'has_actual_record' => false,
            'based_on_period' => null
        ];
        self::$virtualBalanceCache[$cacheKey] = $result;
        return $result;
    }

    /**
     * 从缓存获取科目信息
     * @param int $subjectId 科目ID
     * @return object
     */
    private function getSubjectFromCache($subjectId)
    {
        if (!isset(self::$subjectCache[$subjectId])) {
            self::$subjectCache[$subjectId] = \app\admin\model\wefinancial\Subject::find($subjectId);
        }
        return self::$subjectCache[$subjectId];
    }

    /**
     * 清空虚拟余额缓存（在数据更新后调用）
     */
    public static function clearVirtualBalanceCache()
    {
        self::$virtualBalanceCache = [];
        self::$subjectCache = [];
    }

    /**
     * 获取当前期间
     * 当前期间的定义：最后一个已结账期间的下一个期间，或者尚未结账的会计初始启用期间
     * @return string|null 当前期间 (格式: YYYY-MM)
     */
    public function getCurrentPeriod()
    {
        $periodModel = new \app\admin\model\wefinancial\Period();
        return $periodModel->getCurrentPeriod();
    }

    /**
     * 检查指定科目在指定期间之后是否有业务发生或余额变动
     * @param array $subjectIds 科目ID数组
     * @param string $period 指定期间
     * @return array 检查结果
     */
    public function checkSubsequentPeriodActivity($subjectIds, $period)
    {
        $result = [
            'has_activity' => false,
            'affected_subjects' => [],
            'details' => []
        ];

        if (empty($subjectIds)) {
            return $result;
        }

        // 检查期间余额表中是否有后续期间的记录
        $subsequentBalances = self::whereIn('subject_id', $subjectIds)
            ->where('period', '>', $period)
            ->field('subject_id,period,debit_total,credit_total')
            ->with(['subject' => function($query) {
                $query->field('id,code,name');
            }])
            ->select();

        if (!empty($subsequentBalances)) {
            $result['has_activity'] = true;
            foreach ($subsequentBalances as $balance) {
                $subjectId = $balance->subject_id;
                if (!in_array($subjectId, $result['affected_subjects'])) {
                    $result['affected_subjects'][] = $subjectId;
                }

                $result['details'][] = [
                    'subject_id' => $subjectId,
                    'subject_code' => $balance->subject->code ?? '',
                    'subject_name' => $balance->subject->name ?? '',
                    'period' => $balance->period,
                    'debit_total' => $balance->debit_total,
                    'credit_total' => $balance->credit_total,
                    'type' => 'balance_activity'
                ];
            }
        }

        // 检查是否有后续期间的已记账凭证
        $subsequentVouchers = \think\Db::name('wefinancial_voucher')
            ->alias('v')
            ->join('wefinancial_voucher_entry e', 'v.id = e.voucher_id')
            ->join('wefinancial_subject s', 'e.subject_id = s.id')
            ->where('v.period', '>', $period)
            ->where('v.status', 2) // 已记账状态
            ->whereIn('e.subject_id', $subjectIds)
            ->field('e.subject_id,s.code as subject_code,s.name as subject_name,v.period,v.voucher_no')
            ->group('e.subject_id,v.period,v.voucher_no')
            ->select();

        if (!empty($subsequentVouchers)) {
            $result['has_activity'] = true;
            foreach ($subsequentVouchers as $voucher) {
                $subjectId = $voucher['subject_id'];
                if (!in_array($subjectId, $result['affected_subjects'])) {
                    $result['affected_subjects'][] = $subjectId;
                }

                $result['details'][] = [
                    'subject_id' => $subjectId,
                    'subject_code' => $voucher['subject_code'],
                    'subject_name' => $voucher['subject_name'],
                    'period' => $voucher['period'],
                    'voucher_no' => $voucher['voucher_no'],
                    'type' => 'voucher_activity'
                ];
            }
        }

        return $result;
    }

}
