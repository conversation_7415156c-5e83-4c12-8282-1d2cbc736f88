<?php

namespace app\admin\model\wefinancial;

use think\Model;
use think\model\relation\BelongsTo;
use traits\model\SoftDelete;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\Db;
use addons\wefinancial\library\WeTree;

class Subject extends Model
{

    use SoftDelete;


    // 表名
    protected $name = 'wefinancial_subject';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'balance_direction_text',
        'status_text',
        'cash_flag_text',
        'aux_type_ids_text'
    ];


    public function getBalanceDirectionList()
    {
        return ['借' => __('借'), '贷' => __('贷')];
    }


    public function getLeafFlagList()
    {
        return ['0' => __('Leaf_flag 0'), '1' => __('Leaf_flag 1')];
    }

    public function getCashFlagList()
    {
        return ['1' => __('Cash_flag 1'), '0' => __('Cash_flag 0')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '0' => __('Status 0')];
    }


    public function getBalanceDirectionTextAttr($value, $data)
    {
        $value = $value ?: ($data['balance_direction'] ?? '');
        $list = $this->getBalanceDirectionList();
        return $list[$value] ?? '';
    }

    public function getCashFlagTextAttr($value, $data)
    {
        $value = $value ?: ($data['cash_flag'] ?? '');
        $list = $this->getCashFlagList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    public function getAuxTypeIdsTextAttr($value, $data)
    {
        if(empty($data['aux_type_ids'])){
            return '';
        }
        $auxTypeIds = explode(',', $data['aux_type_ids']);
        $auxTypeNames = [];
        foreach ($auxTypeIds as $auxTypeId) {
            $auxType = AuxiliaryType::where('id', $auxTypeId)->find();
            if ($auxType) {
                $auxTypeNames[] = $auxType->name;
            }
        }
        return implode(',', $auxTypeNames);
    }


    public function type(): BelongsTo
    {
        return $this->belongsTo(SubjectType::class, 'type_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 获取科目的辅助核算类型（包括继承自上级的）
     * @param int $subjectId 科目ID
     * @return array 辅助核算类型ID数组
     */
    public static function getInheritedAuxTypes($subjectId)
    {
        $subject = self::where('id', $subjectId)->field('id,pid,aux_type_ids')->find();
        if (!$subject) {
            return [];
        }

        $auxTypeIds = [];

        // 获取当前科目的辅助核算类型
        if (!empty($subject->aux_type_ids)) {
            $currentAuxTypes = explode(',', $subject->aux_type_ids);
            $auxTypeIds = array_merge($auxTypeIds, $currentAuxTypes);
        }

        // 向上查找父级科目的辅助核算类型
        $currentSubject = $subject;
        while ($currentSubject->pid > 0) {
            $parentSubject = self::where('id', $currentSubject->pid)->field('id,pid,aux_type_ids')->find();
            if (!$parentSubject) {
                break;
            }

            if (!empty($parentSubject->aux_type_ids)) {
                $parentAuxTypes = explode(',', $parentSubject->aux_type_ids);
                $auxTypeIds = array_merge($auxTypeIds, $parentAuxTypes);
            }

            $currentSubject = $parentSubject;
        }

        // 去重并过滤空值
        $auxTypeIds = array_unique(array_filter($auxTypeIds));

        return array_values($auxTypeIds);
    }

    /**
     * 检查科目是否为叶子节点
     * @param int $subjectId 科目ID
     * @return bool
     */
    public static function isLeafSubject($subjectId)
    {
        $subject = self::where('id', $subjectId)->field('leaf_flag')->find();
        return $subject && $subject->leaf_flag == 1;
    }

    /**
     * Excel导入科目数据
     * @param string $filePath Excel文件路径
     * @return array 返回结果 ['success' => bool, 'message' => string]
     */
    public function importFromExcel($filePath)
    {
        if (!file_exists($filePath)) {
            return ['success' => false, 'message' => 'Excel文件不存在：' . $filePath];
        }

        try {
            // 1. 清空subject与subject_type表
            $this->clearTables();

            // 2. 读取Excel文件
            $spreadsheet = IOFactory::load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // 3. 忽略第一行，处理数据
            $dataRows = array_slice($rows, 1);

            // 4. 处理科目类别和科目数据
            $this->processExcelData($dataRows);

            return ['success' => true, 'message' => 'Excel导入成功'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => 'Excel导入失败：' . $e->getMessage()];
        }
    }

    /**
     * 从数组数据导入科目（用于测试或直接数据导入）
     * @param array $dataRows 数据行数组
     * @return array 返回结果 ['success' => bool, 'message' => string]
     */
    public function importFromArray($dataRows)
    {
        try {
            // 开启事务
            Db::startTrans();

            // 1. 清空subject与subject_type表
            $this->clearTables();

            // 2. 处理科目类别和科目数据
            $this->processExcelData($dataRows);

            Db::commit();
            return ['success' => true, 'message' => '数据导入成功'];

        } catch (\Exception $e) {
            Db::rollback();
            return ['success' => false, 'message' => '数据导入失败：' . $e->getMessage()];
        }
    }

    /**
     * 清空subject与subject_type表
     */
    private function clearTables()
    {
        // 清空科目表
        Db::execute('TRUNCATE TABLE fa_wefinancial_subject');

        // 清空科目类别表
        Db::execute('TRUNCATE TABLE fa_wefinancial_subject_type');
    }

    /**
     * 处理Excel数据
     * @param array $dataRows Excel数据行
     * @throws \Exception
     */
    private function processExcelData($dataRows)
    {
        $subjectTypeMap = []; // 科目类别映射 [类别名称 => ID]
        $subjectData = []; // 科目数据

        // 第一遍：收集所有科目类别
        foreach ($dataRows as $row) {
            if (empty($row[0])) continue; // 跳过空行

            $firstLevelType = trim($row[2] ?? ''); // 一级科目类别
            $secondLevelType = trim($row[3] ?? ''); // 二级科目类别

            // 收集科目类别
            if (!empty($firstLevelType)) {
                $subjectTypeMap[$firstLevelType] = null;
            }
            if (!empty($secondLevelType)) {
                $subjectTypeMap[$secondLevelType] = null;
            }
        }

        // 创建科目类别（先创建一级，再创建二级）
        $this->createSubjectTypes($dataRows, $subjectTypeMap);

        // 收集科目数据
        foreach ($dataRows as $row) {
            if (empty($row[0])) continue; // 跳过空行

            $code = trim($row[0] ?? ''); // 科目编码
            $name = trim($row[1] ?? ''); // 科目名称
            $firstLevelType = trim($row[2] ?? ''); // 一级科目类别
            $secondLevelType = trim($row[3] ?? ''); // 二级科目类别
            $balanceDirection = trim($row[4] ?? ''); // 余额方向
            $cashFlag = trim($row[6] ?? ''); // 是否现金科目
            $status = trim($row[7] ?? ''); // 状态

            // 确定使用哪个科目类别（优先使用二级类别）
            $typeName = !empty($secondLevelType) ? $secondLevelType : $firstLevelType;
            $typeId = $subjectTypeMap[$typeName] ?? 0;

            $subjectData[] = [
                'code' => $code,
                'name' => $name,
                'type_id' => $typeId,
                'balance_direction' => $balanceDirection === '借' ? '借' : '贷',
                'cash_flag' => $cashFlag === '是' ? 1 : 0,
                'status' => $status === '启用' ? 1 : 0,
                'aux_type_ids' => '', // 暂时忽略辅助核算
            ];
        }

        // 按编码排序，确保父科目先于子科目创建
        usort($subjectData, function($a, $b) {
            return strcmp($a['code'], $b['code']);
        });

        // 创建科目（按编码顺序处理层级关系）
        $this->createSubjects($subjectData);
    }

    /**
     * 创建科目类别
     * @param array $dataRows Excel数据行
     * @param array $subjectTypeMap 科目类别映射（引用传递）
     */
    private function createSubjectTypes($dataRows, &$subjectTypeMap)
    {
        $firstLevelTypes = []; // 一级类别
        $secondLevelTypes = []; // 二级类别及其父级关系

        // 收集一级和二级类别
        foreach ($dataRows as $row) {
            if (empty($row[0])) continue;

            $firstLevelType = trim($row[2] ?? '');
            $secondLevelType = trim($row[3] ?? '');

            if (!empty($firstLevelType)) {
                $firstLevelTypes[$firstLevelType] = true;
            }

            if (!empty($secondLevelType) && !empty($firstLevelType)) {
                $secondLevelTypes[$secondLevelType] = $firstLevelType;
            }
        }

        // 先创建一级类别
        foreach ($firstLevelTypes as $typeName => $v) {
            $typeId = Db::name('wefinancial_subject_type')->insertGetId([
                'name' => $typeName,
                'pid' => 0,
                'status' => 1,
                'createtime' => time(),
                'updatetime' => time()
            ]);
            $subjectTypeMap[$typeName] = $typeId;
        }

        // 再创建二级类别
        foreach ($secondLevelTypes as $typeName => $parentName) {
            $parentId = $subjectTypeMap[$parentName] ?? 0;
            $typeId = Db::name('wefinancial_subject_type')->insertGetId([
                'name' => $typeName,
                'pid' => $parentId,
                'status' => 1,
                'createtime' => time(),
                'updatetime' => time()
            ]);
            $subjectTypeMap[$typeName] = $typeId;
        }
    }

    /**
     * 创建科目
     * @param array $subjectData 科目数据
     */
    private function createSubjects($subjectData)
    {
        $codeToIdMap = []; // 编码到ID的映射

        foreach ($subjectData as $data) {
            $code = $data['code'];

            // 根据编码规则确定父科目
            $parentId = WeTree::getParentSubjectIdByCode($code, $codeToIdMap);

            // 计算层级
            $level = WeTree::calculateSubjectLevelByCode($code);

            // 插入科目数据
            $subjectId = Db::name('wefinancial_subject')->insertGetId([
                'code' => $data['code'],
                'name' => $data['name'],
                'type_id' => $data['type_id'],
                'pid' => $parentId,
                'level' => $level,
                'balance_direction' => $data['balance_direction'],
                'leaf_flag' => 1, // 默认为末级科目
                'cash_flag' => $data['cash_flag'],
                'aux_type_ids' => $data['aux_type_ids'],
                'status' => $data['status'],
                'createtime' => time(),
                'updatetime' => time()
            ]);

            // 记录编码到ID的映射
            $codeToIdMap[$code] = $subjectId;

            // 如果有父科目，将父科目设置为非末级科目
            if ($parentId > 0) {
                Db::name('wefinancial_subject')
                    ->where('id', $parentId)
                    ->update(['leaf_flag' => 0]);
            }
        }
    }



}
