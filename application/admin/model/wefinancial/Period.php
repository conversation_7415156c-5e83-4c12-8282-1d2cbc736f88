<?php

namespace app\admin\model\wefinancial;

use think\Model;
use think\Db;

class Period extends Model
{

    // 表名
    protected $name = 'wefinancial_period';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];



    public function getStatusList()
    {
        return ['1' => '已结账', '0' => '未结账'];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    /**
     * 创建初始期间记录
     * @param string $period 期间 (格式: YYYY-MM)
     * @return bool
     * @throws \Exception
     */
    public function createInitialPeriod($period)
    {
        // 检查期间格式
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            throw new \Exception('期间格式错误，请使用YYYY-MM格式');
        }

        // 检查是否已存在该期间记录
        $existingPeriod = $this->where('period', $period)->find();
        if ($existingPeriod) {
            return true; // 已存在，直接返回成功
        }

        try {
            // 创建期间记录
            $data = [
                'period' => $period,
                'status' => 0, // 未结账
                'remark' => '系统自动创建的启用期间'
            ];

            $this->save($data);
            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 检查是否存在任何期间记录
     * @return bool
     */
    public function hasAnyPeriodRecords()
    {
        return $this->count() > 0;
    }

    /**
     * 获取最早的期间记录
     * @return string|null
     */
    public function getEarliestPeriod()
    {
        $period = $this->order('period', 'asc')->find();
        return $period ? $period->period : null;
    }

    /**
     * 检查期间是否可以结账
     * @param string $period 期间
     * @return array
     */
    public function canClosePeriod($period)
    {
        $periodModel = $this->where('period', $period)->find();
        if (!$periodModel) {
            return ['success' => false, 'message' => '期间不存在'];
        }

        if ($periodModel->status == 1) {
            return ['success' => false, 'message' => '该期间已经结账'];
        }

        // 检查上期是否已结账
        $previousPeriod = $this->getPreviousPeriod($period);
        if ($previousPeriod) {
            $prevPeriodModel = $this->where('period', $previousPeriod)->find();
            if ($prevPeriodModel && $prevPeriodModel->status == 0) {
                return ['success' => false, 'message' => "上期 {$previousPeriod} 尚未结账"];
            }
        }

        // 检查是否有未记账凭证
        $voucherModel = new \app\admin\model\wefinancial\Voucher;
        $unpostedCount = $voucherModel->where('period', $period)
            ->where('status', '<', 2)
            ->count();

        if ($unpostedCount > 0) {
            return ['success' => false, 'message' => "存在 {$unpostedCount} 张未记账凭证"];
        }

        return ['success' => true];
    }

    /**
     * 检查期间是否可以反结账
     * @param string $period 期间
     * @return array
     */
    public function canUnclosePeriod($period)
    {
        $periodModel = $this->where('period', $period)->find();
        if (!$periodModel) {
            return ['success' => false, 'message' => '期间不存在'];
        }

        if ($periodModel->status == 0) {
            return ['success' => false, 'message' => '该期间未结账'];
        }

        // 检查下期是否有业务发生
        $nextPeriod = $this->getNextPeriod($period);
        if ($nextPeriod) {
            $voucherModel = new \app\admin\model\wefinancial\Voucher;
            $nextPeriodVouchers = $voucherModel->where('period', $nextPeriod)->count();

            if ($nextPeriodVouchers > 0) {
                return ['success' => false, 'message' => "下期 {$nextPeriod} 已有业务发生"];
            }
        }

        return ['success' => true];
    }

    /**
     * 获取上一期间
     * @param string $period 当前期间
     * @return string
     */
    public function getPreviousPeriod($period)
    {
        $year = substr($period, 0, 4);
        $month = substr($period, 5, 2);

        $prevMonth = intval($month) - 1;
        if ($prevMonth == 0) {
            $prevMonth = 12;
            $year = intval($year) - 1;
        }

        return sprintf('%04d-%02d', $year, $prevMonth);
    }

    /**
     * 获取下一期间
     * @param string $period 当前期间
     * @return string
     */
    public function getNextPeriod($period)
    {
        $year = substr($period, 0, 4);
        $month = substr($period, 5, 2);

        $nextMonth = intval($month) + 1;
        if ($nextMonth == 13) {
            $nextMonth = 1;
            $year = intval($year) + 1;
        }

        return sprintf('%04d-%02d', $year, $nextMonth);
    }

    /**
     * 执行期间结账
     * @param string $period 期间
     * @return bool
     * @throws \Exception
     */
    public function closePeriod($period)
    {
        // 检查是否可以结账
        $checkResult = $this->canClosePeriod($period);
        if (!$checkResult['success']) {
            throw new \Exception($checkResult['message']);
        }

        Db::startTrans();
        try {
            // 更新期间状态
            $this->where('period', $period)->update([
                'status' => 1,
                'remark' => '期间结账于 ' . date('Y-m-d H:i:s')
            ]);

            // 创建下期期间记录
            $nextPeriod = $this->getNextPeriod($period);
            if ($nextPeriod) {
                $existingNext = $this->where('period', $nextPeriod)->find();
                if (!$existingNext) {
                    $this->create([
                        'period' => $nextPeriod,
                        'status' => 0,
                        'remark' => '系统自动创建'
                    ]);
                }
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 执行期间反结账
     * @param string $period 期间
     * @return bool
     * @throws \Exception
     */
    public function unclosePeriod($period)
    {
        // 检查是否可以反结账
        $checkResult = $this->canUnclosePeriod($period);
        if (!$checkResult['success']) {
            throw new \Exception($checkResult['message']);
        }

        Db::startTrans();
        try {
            // 更新期间状态
            $this->where('period', $period)->update([
                'status' => 0,
                'remark' => '期间反结账于 ' . date('Y-m-d H:i:s')
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 获取当前期间
     * 当前期间的定义：最后一个已结账期间的下一个期间，或者尚未结账的会计初始启用期间
     * @return string|null 当前期间 (格式: YYYY-MM)
     */
    public function getCurrentPeriod()
    {
        // 查找最后一个已结账的期间
        $lastClosedPeriod = $this->where('status', 1)
            ->order('period', 'desc')
            ->find();

        if ($lastClosedPeriod) {
            // 如果存在已结账期间，返回其下一个期间
            return $this->getNextPeriod($lastClosedPeriod->period);
        }

        // 如果没有已结账期间，查找最早的未结账期间（即启用期间）
        $earliestUnClosedPeriod = $this->where('status', 0)
            ->order('period', 'asc')
            ->find();

        if ($earliestUnClosedPeriod) {
            return $earliestUnClosedPeriod->period;
        }

        // 如果没有任何期间记录，返回null
        return null;
    }

    /**
     * 获取期间的第一天日期
     * @param string $period 期间 (格式: YYYY-MM)
     * @return string 日期 (格式: YYYY-MM-DD)
     */
    public function getPeriodFirstDay($period)
    {
        if (empty($period)) {
            return null;
        }

        // 检查期间格式
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            return null;
        }

        return $period . '-01';
    }

    /**
     * 获取当前期间的第一天日期
     * @return string|null 日期 (格式: YYYY-MM-DD)
     */
    public function getCurrentPeriodFirstDay()
    {
        $currentPeriod = $this->getCurrentPeriod();
        if (empty($currentPeriod)) {
            return null;
        }

        return $this->getPeriodFirstDay($currentPeriod);
    }

}
