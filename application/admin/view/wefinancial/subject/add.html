<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-type_id" data-rule="required" class="form-control selectpicker" name="row[type_id]">
                {foreach name="typeList" item="vo"}
                <option value="{$vo.id|htmlentities}" {in name="$vo.id" value=""}selected{/in}>{$vo.name|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-pid" data-rule="required" class="form-control selectpicker" name="row[pid]">
                {foreach name="parentList" item="vo"}
                <option data-type="{$vo.type_id|htmlentities}" value="{$key|htmlentities}" {in name="key" value=""}selected{/in}>{$vo.name|htmlentities}</option>
                {/foreach}
            </select>
        </div>

    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" data-rule="required" class="form-control" name="row[code]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance_direction')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                {foreach name="balanceDirectionList" item="vo"}
                <label for="row[balance_direction]-{$key|htmlentities}"><input id="row[balance_direction]-{$key|htmlentities}" data-rule="checked"  name="row[balance_direction]" type="radio" value="{$key|htmlentities}" /> {$vo|htmlentities}</label>
                {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cash_flag')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                {foreach name="cashFlagList" item="vo"}
                <label for="row[cash_flag]-{$key|htmlentities}"><input id="row[cash_flag]-{$key|htmlentities}" name="row[cash_flag]" data-rule="checked" type="radio" value="{$key|htmlentities}" /> {$vo|htmlentities}</label>
                {/foreach}
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Aux_type_ids')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-aux_type_ids" data-source="wefinancial/auxiliary_type/index" data-order-by="id asc" data-multiple="true" class="form-control selectpage" name="row[aux_type_ids]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key|htmlentities}"><input id="row[status]-{$key|htmlentities}" name="row[status]" type="radio" value="{$key|htmlentities}" {in name="key" value="1"}checked{/in} /> {$vo|htmlentities}</label>
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
