<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Template_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-template_id" data-rule="required" data-source="template/index" class="form-control selectpage" name="row[template_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Column_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-column_name" data-rule="required" class="form-control" name="row[column_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Column_position')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-column_position" data-rule="required" class="form-control" name="row[column_position]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Data_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-data_type" class="form-control selectpicker" name="row[data_type]">
                {foreach name="dataTypeList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="amount"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Period_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-period_type" data-rule="required" class="form-control selectpicker" name="row[period_type]">
                {foreach name="periodTypeList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="custom"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-balance_type" class="form-control selectpicker" name="row[balance_type]">
                {foreach name="balanceTypeList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="period"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Decimal_places')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-decimal_places" class="form-control" name="row[decimal_places]" type="number" value="2">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Show_unit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-show_unit" class="form-control" name="row[show_unit]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Width')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-width" class="form-control" name="row[width]" type="number" value="120">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key|htmlentities}"><input id="row[status]-{$key|htmlentities}" name="row[status]" type="radio" value="{$key|htmlentities}" {in name="key" value="1"}checked{/in} /> {$vo|htmlentities}</label>
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
