<form id="subject-config-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
  <div class="row">
    <div class="col-md-12">
      <button type="button" class="btn btn-success btn-sm btn-add-subject" style="margin-bottom: 15px;">
        <i class="fa fa-plus"></i> 添加科目
      </button>
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-bordered" id="subject-config-table">
      <thead>
      <tr>
        <th width="45%">科目</th>
        <th width="15%">运算类型</th>
        <th width="15%">取数方向</th>
        <th width="15%">权重系数</th>
        <th width="10%">操作</th>
      </tr>
      </thead>
      <tbody id="subject-config-tbody">
      {if condition="empty($subjects)"}
      <tr>
        <td colspan="6" class="text-center text-muted">暂无科目配置</td>
      </tr>
      {else}
      {volist name="subjects" id="subject"}
      <tr>
        <td>
          <input type="text"
                 id="c-subject_id_{$subject.subject_id|htmlentities}"
                 class="form-control selectpage subject-selector"
                 data-source="wefinancial/subject/index"
                 data-primary-key="id"
                 data-field="name"
                 name="subjects[{$key|htmlentities}][subject_id]"
                 data-order-by="code asc"
                 data-params='{"status": 1}'
                 data-format-item="{code} {name}"
                 value="{$subject.subject_id|htmlentities}"
                 placeholder="请选择科目"
          >
        </td>
        <td>
          <select class="form-control operation-type" name="operation_type">
            <option value="add" {eq name="subject.operation_type" value="add" }selected{/eq}>加</option>
            <option value="subtract" {eq name="subject.operation_type" value="subtract" }selected{/eq}>减</option>
          </select>
        </td>
        <td>
          <select class="form-control balance-direction" name="balance_direction">
            <option value="balance" {eq name="subject.balance_direction" value="balance" }selected{/eq}>余额</option>
            <option value="debit" {eq name="subject.balance_direction" value="debit" }selected{/eq}>借方</option>
            <option value="credit" {eq name="subject.balance_direction" value="credit" }selected{/eq}>贷方</option>
          </select>
        </td>
        <td>
          <input type="number" class="form-control weight" name="weight" value="{$subject.weight|htmlentities}" step="0.0001"
                 min="0">
        </td>
        <td>
          <button type="button" class="btn btn-danger btn-xs btn-remove-subject">删除</button>
        </td>
      </tr>
      {/volist}
      {/if}
      </tbody>
    </table>
  </div>

  <div class="form-group layer-footer">
    <label class="control-label col-xs-12 col-sm-2"></label>
    <div class="col-xs-12 col-sm-8">
      <button type="button" class="btn btn-primary btn-save-config">保存</button>
    </div>
  </div>
</form>

<!-- 科目配置行模板 -->
<script type="text/template" id="subject-row-template">
  <tr>
    <td>
      <input type="text"
             class="form-control selectpage subject-selector"
             data-source="wefinancial/subject/index"
             id="c-subject_id-<%=__RAND__%>"
             name="subjects[<%=__RAND__%>][subject_id]"
             data-primary-key="id"
             data-order-by="code asc"
             data-params='{"status": 1}'
             data-format-item="{code} {name}"
             placeholder="请选择科目"
      >
    </td>
    <td>
      <select class="form-control operation-type" name="operation_type">
        <option value="add">加</option>
        <option value="subtract">减</option>
      </select>
    </td>
    <td>
      <select class="form-control balance-direction" name="balance_direction">
        <option value="balance">余额</option>
        <option value="debit">借方</option>
        <option value="credit">贷方</option>
      </select>
    </td>
    <td>
      <input type="number" class="form-control weight" name="weight" value="1.0000" step="0.0001" min="0">
    </td>
    <td>
      <button type="button" class="btn btn-danger btn-xs btn-remove-subject">删除</button>
    </td>
  </tr>
</script>
