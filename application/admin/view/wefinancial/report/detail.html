<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <h4>{$template.name|htmlentities}</h4>
    </div>
    <div class="panel-body">
        <!-- 报表头部信息 -->
        <div class="row" style="margin-bottom: 20px;">
            <div class="col-xs-8">
                <div class="form-inline">
                    <div class="form-group">
                        <label for="period-select">会计期间：</label>
                        <select id="period-select" class="form-control" style="width: 150px;">
                            {volist name="periods" id="periodItem"}
                            <option value="{$periodItem.period|htmlentities}" {eq name="periodItem.period" value="$period"}selected{/eq}>{$periodItem.period|htmlentities}</option>
                            {/volist}
                        </select>
                    </div>
                    <div class="form-group" style="margin-left: 20px;">
                        <label>会计制度：</label>
                        <span class="form-control-static">{$template.accounting_system|htmlentities}</span>
                    </div>
                </div>
            </div>
            <div class="col-xs-4 text-right">
                <div class="operation-buttons no-print">
                    <button type="button" class="btn btn-primary btn-export">
                        <i class="fa fa-download"></i> 导出Excel
                    </button>
                    <button type="button" class="btn btn-info btn-print">
                        <i class="fa fa-print"></i> 打印
                    </button>
                </div>
            </div>
        </div>

        <!-- 打印区域开始 -->
        <div id="print-area">
            <!-- 打印头部标题 -->
            <div class="print-header text-center" style="margin-bottom: 20px;">
                <h3 class="report-title" style="margin: 0 0 15px 0; font-weight: bold;">{$template.type|htmlentities}</h3>
                <div class="row report-info" style="margin-bottom: 15px;">
                    <div class="col-xs-4 text-left">
                        <span class="company-name">编制单位：<span id="company-name-text">{$companyName|htmlentities}</span></span>
                    </div>
                    <div class="col-xs-4 text-center">
                        <span class="report-period">报表期间：<span id="report-period-text">{$period|htmlentities}</span></span>
                    </div>
                    <div class="col-xs-4 text-right">
                        <span class="report-unit">单位：元</span>
                    </div>
                </div>
            </div>

            <!-- 报表内容 -->
            <div class="row">
                <div class="col-xs-12">
                    <div id="report-content">
                        {if condition="empty($reportData)"}
                        <div class="alert alert-info">暂无报表数据</div>
                        {else}
                        {if condition="$template.type == '资产负债表'"}
                        {include file="wefinancial/report/balance_sheet_table" /}
                        {else}
                        {include file="wefinancial/report/single_column_table" /}
                        {/if}
                        {/if}
                    </div>
                </div>
            </div>
        </div>
        <!-- 打印区域结束 -->
    </div>
</div>
<script>
    var templateId = '{$template.id|htmlentities}';

</script>
