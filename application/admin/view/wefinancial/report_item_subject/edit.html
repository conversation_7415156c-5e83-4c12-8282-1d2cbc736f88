<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Item_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-item_id" data-rule="required" data-source="item/index" class="form-control selectpage" name="row[item_id]" type="text" value="{$row.item_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Subject_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-subject_id" data-rule="required" data-source="subject/index" class="form-control selectpage" name="row[subject_id]" type="text" value="{$row.subject_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Operation_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-operation_type" class="form-control selectpicker" name="row[operation_type]">
                {foreach name="operationTypeList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="$row.operation_type"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance_direction')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-balance_direction" class="form-control selectpicker" name="row[balance_direction]">
                {foreach name="balanceDirectionList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="$row.balance_direction"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-balance_type" class="form-control selectpicker" name="row[balance_type]">
                {foreach name="balanceTypeList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="$row.balance_type"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weight" class="form-control" step="0.0001" name="row[weight]" type="number" value="{$row.weight|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key|htmlentities}"><input id="row[status]-{$key|htmlentities}" name="row[status]" type="radio" value="{$key|htmlentities}" {in name="key" value="$row.status"}checked{/in} /> {$vo|htmlentities}</label>
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
