<div class="row">
    <div class="col-xs-12">
        <div class="box">
            <div class="box-header">
                <h3 class="box-title">财务系统初始化设置</h3>
            </div>
            <div class="box-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    <strong>说明：</strong>
                    <ul class="mb-0 mt-1">
                        <li>请完整填写以下初始化信息，这些信息将用于财务系统的基础配置</li>
                        <li>启用期间将作为财务账套的起始期间，请谨慎设置</li>
                        <li>纳税人信息将用于报表生成和税务申报</li>
                        <li>会计制度选择将影响科目体系和报表格式</li>
                        <li>设置完成后，建议先进行科目初始余额设置</li>
                    </ul>
                </div>


                <form id="settings-form" class="form-horizontal settings-form" role="form" data-toggle="validator" method="POST" action="{:url('wefinancial/settings/save')}">

                    <!-- 基本信息 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <i class="fa fa-cog"></i> 基本设置
                            </h4>
                        </div>
                        <div class="panel-body">

                            <!-- 启用期间 -->
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">启用期间:</label>
                                <div class="col-xs-12 col-sm-4">
                                    <input id="c-initial_period"
                                           data-rule="required"
                                           class="form-control"
                                           name="initial_period"
                                           type="text"
                                           value="{$addonConfig.initial_period|default=''}"
                                           placeholder="YYYY-MM"
                                           pattern="^\d{4}-\d{2}$"
                                           {if condition="$hasPeriodRecords"}readonly{/if}>
                                    <span class="help-block">格式：YYYY-MM，如：2025-01</span>
                                </div>
                                <div class="col-xs-12 col-sm-6">
                                    {if condition="$hasPeriodRecords"}
                                    <div class="alert alert-info" style="margin-bottom: 0; padding: 8px 12px;">
                                        <i class="fa fa-info-circle"></i> 系统已生成会计期间记录，启用期间不可修改
                                        {if condition="$earliestPeriod"}
                                        <br><small>当前启用期间：{$earliestPeriod|htmlentities}</small>
                                        {/if}
                                    </div>
                                    {else /}
                                    <div class="alert alert-warning" style="margin-bottom: 0; padding: 8px 12px;">
                                        <i class="fa fa-warning"></i> 启用期间设置后将影响整个财务系统，请谨慎填写
                                    </div>
                                    {/if}
                                </div>
                            </div>

                            <!-- 会计制度 -->
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">会计制度:</label>
                                <div class="col-xs-12 col-sm-4">
                                    <select id="c-accounting_system"
                                            data-rule="required"
                                            class="form-control selectpicker"
                                            name="accounting_system"
                                            {if condition="$hasPeriodRecords"}disabled style="pointer-events: none; background-color: #f5f5f5;"{/if}>
                                        {foreach name="accountingSystemList" item="vo"}
                                        <option value="{$key|htmlentities}" {in name="key" value="$addonConfig.accounting_system"}selected{/in}>{$vo|htmlentities}</option>
                                        {/foreach}
                                    </select>
                                </div>
                                <div class="col-xs-12 col-sm-6">
                                    {if condition="$hasPeriodRecords"}
                                    <div class="alert alert-warning" style="margin-bottom: 0; padding: 8px 12px;">
                                        <i class="fa fa-warning"></i> 会计制度设置后将影响科目体系，请谨慎选择
                                    </div>
                                    {else /}
                                    <span class="help-block">根据企业性质选择适用的会计制度，设置后将自动导入对应科目</span>
                                    {/if}
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- 企业信息 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <i class="fa fa-building"></i> 企业信息
                            </h4>
                        </div>
                        <div class="panel-body">

                            <!-- 公司名称 -->
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">公司名称:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input id="c-company_name"
                                           data-rule="required"
                                           class="form-control"
                                           name="company_name"
                                           type="text"
                                           value="{$addonConfig.company_name|default=''}"
                                           placeholder="请输入公司全称">
                                </div>
                            </div>

                            <!-- 纳税人名称 -->
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">纳税人名称:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input id="c-tax_name"
                                           data-rule="required"
                                           class="form-control"
                                           name="tax_name"
                                           type="text"
                                           value="{$addonConfig.tax_name|default=''}"
                                           placeholder="请输入纳税人名称">
                                    <span class="help-block">通常与公司名称一致</span>
                                </div>
                            </div>

                            <!-- 纳税人识别号 -->
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">纳税人识别号:</label>
                                <div class="col-xs-12 col-sm-6">
                                    <input id="c-tax_no"
                                           data-rule="required"
                                           class="form-control"
                                           name="tax_no"
                                           type="text"
                                           value="{$addonConfig.tax_no|default=''}"
                                           placeholder="请输入统一社会信用代码或税务登记号">
                                    <span class="help-block">统一社会信用代码或税务登记号</span>
                                </div>
                            </div>

                            <!-- 纳税人性质 -->
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">纳税人性质:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class="radio">
                                        {foreach name="taxTypeList" item="vo"}
                                        <label for="tax_type-{$key|htmlentities}" class="radio-inline">
                                            <input id="tax_type-{$key|htmlentities}"
                                                   name="tax_type"
                                                   type="radio"
                                                   value="{$key|htmlentities}"
                                                   {in name="key" value="$addonConfig.tax_type|default='1'"}checked{/in} />
                                            {$vo|htmlentities}
                                        </label>
                                        {/foreach}
                                    </div>
                                    <span class="help-block">影响税率计算和申报表格式</span>
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="form-group">
                        <div class="col-xs-12 text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fa fa-save"></i> 保存初始化设置
                            </button>
                            <button type="button" class="btn btn-default btn-lg" onclick="history.back()">
                                <i class="fa fa-arrow-left"></i> 返回
                            </button>
                        </div>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>
