<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <!-- 凭证基本信息 - 紧凑布局 -->
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-3">{:__('Voucher_date')}:</label>
                <div class="col-xs-12 col-sm-9">
                    <input id="c-voucher_date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[voucher_date]" type="text" value="{$defaultDate|default=date('Y-m-d')}">
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-4">{:__('Voucher_type')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <select id="c-voucher_type" data-rule="required" class="form-control selectpicker" name="row[voucher_type]">
                        {foreach name="voucherTypeList" item="vo"}
                        <option value="{$key|htmlentities}" {in name="key" value="记"}selected{/in}>{$vo|htmlentities}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('Summary')}:</label>
                <div class="col-xs-12 col-sm-10">
                    <input id="c-summary" class="form-control" name="row[summary]" type="text" placeholder="请输入凭证摘要">
                </div>
            </div>
        </div>

    </div>




    <!-- 凭证分录 -->
    <div class="form-group">
        <div class="col-xs-12 col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading clearfix">
                    <h4 class="panel-title pull-left panel-title-aligned">
                        分录明细
                    </h4>
                    <button type="button" class="btn btn-success btn-add btn-sm pull-right" id="add-entry">
                        <i class="fa fa-plus"></i> 添加分录
                    </button>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="entries-table">
                            <thead>
                                <tr>
                                    <th width="25%" align="center">科目</th>
                                    <th width="20%">摘要</th>
                                    <th width="12%">借方金额</th>
                                    <th width="12%">贷方金额</th>
                                    <th width="25%">辅助核算</th>
                                    <th width="6%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="entries-tbody">
                                <!-- 分录行将通过JavaScript动态添加 -->
                            </tbody>
                            <tfoot>
                                <tr class="info">
                                    <td colspan="2"><strong>合计</strong></td>
                                    <td><strong id="debit-total">0.00</strong></td>
                                    <td><strong id="credit-total">0.00</strong></td>
                                    <td colspan="2">
                                        <span id="balance-status" class="label label-warning">未平衡</span>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="form-group">
        <label for="c-attachment_files" class="control-label col-xs-12 col-sm-1">{:__('Attachment_files')}:</label>
        <div class="col-xs-12 col-sm-11">
            <div class="input-group">
                <input id="c-attachment_files" class="form-control" size="50" name="row[attachment_files]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-attachment_files" class="btn btn-danger faupload" data-input-id="c-attachment_files" data-multiple="true" data-preview-id="p-attachment_files"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-attachment_files" class="btn btn-primary fachoose" data-input-id="c-attachment_files" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-attachment_files"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-attachment_files"></ul>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" name="action" value="save" class="btn btn-primary btn-embossed disabled">
                <i class="fa fa-save"></i> 保存
            </button>
            <button type="submit" name="action" value="save_and_audit" class="btn btn-success btn-embossed disabled">
                <i class="fa fa-check"></i> 保存并审核
            </button>
        </div>
    </div>
</form>
